#!/usr/bin/env python3
"""
Dynamic Agent Manager
Manages dynamic sub-agents that connect to user-specific MCP servers
"""
import asyncio
import json
import logging
import uuid
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from server.dynamic_mcp_factory import get_dynamic_mcp_server_info

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DynamicAgentManager")

class DynamicAgent:
    """Individual dynamic agent that connects to a specific MCP server"""
    
    def __init__(self, agent_id: str, user_id: str, server_id: str, agent_name: str):
        self.agent_id = agent_id
        self.user_id = user_id
        self.server_id = server_id
        self.agent_name = agent_name
        self.client_session = None
        self.server_process = None
        self.created_at = datetime.now().isoformat()
        self.last_used = None
        self.status = "created"
        logger.info(f"Dynamic agent created: {agent_id} for user {user_id}")
    
    async def initialize(self) -> bool:
        """Initialize the agent and connect to its MCP server using stdio transport"""
        try:
            # Get server info
            server_info = get_dynamic_mcp_server_info(self.server_id)
            if not server_info:
                logger.error(f"No server info found for server {self.server_id}")
                return False

            logger.info(f"Initializing agent {self.agent_id} with stdio transport")

            # Get the server script path
            from pathlib import Path
            server_dir = Path("dynamic_servers")
            server_script = server_dir / self.server_id / f"{self.server_id}.py"

            if not server_script.exists():
                logger.error(f"Server script not found: {server_script}")
                return False

            # Start the MCP server process with stdio transport
            self.server_process = subprocess.Popen(
                ["python", str(server_script)],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Create stdio client session
            server_params = StdioServerParameters(
                command="python",
                args=[str(server_script)]
            )

            # Connect using stdio client
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    # Initialize the session
                    await session.initialize()

                    # List available tools to verify connection
                    tools_result = await session.list_tools()
                    tools = tools_result.tools if tools_result else []

                    logger.info(f"Agent {self.agent_id} connected to server {self.server_id} with {len(tools)} tools")
                    self.status = "connected"
                    self.client_session = session
                    return True

        except Exception as e:
            logger.error(f"Error initializing agent {self.agent_id}: {str(e)}", exc_info=True)
            self.status = "error"
            if self.server_process:
                self.server_process.terminate()
                self.server_process = None
            return False
    
    async def query(self, query: str, tool_name: str = None) -> Dict[str, Any]:
        """Query the agent's MCP server using stdio transport"""
        try:
            if not self.server_process or self.server_process.poll() is not None:
                return {
                    "success": False,
                    "error": "Agent server not running",
                    "result": "",
                    "agent_type": "dynamic",
                    "agent_id": self.agent_id
                }

            # Get the server script path for stdio connection
            from pathlib import Path
            server_dir = Path("dynamic_servers")
            server_script = server_dir / self.server_id / f"{self.server_id}.py"

            # Create stdio client session for this query
            server_params = StdioServerParameters(
                command="python",
                args=[str(server_script)]
            )

            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    # Initialize the session
                    await session.initialize()

                    # List available tools
                    tools_result = await session.list_tools()
                    tools = tools_result.tools if tools_result else []

                    if not tools:
                        return {
                            "success": False,
                            "error": "No tools available",
                            "result": "",
                            "agent_type": "dynamic",
                            "agent_id": self.agent_id
                        }

                    # Use first available tool if none specified
                    if not tool_name:
                        tool_name = tools[0].name

                    # Find the tool
                    tool = next((t for t in tools if t.name == tool_name), None)
                    if not tool:
                        # Try to find a search or query tool
                        search_tools = [t for t in tools if any(keyword in t.name.lower()
                                                              for keyword in ['search', 'query', 'list', 'get'])]
                        if search_tools:
                            tool = search_tools[0]
                            tool_name = tool.name
                        else:
                            tool = tools[0]
                            tool_name = tool.name

                    # Prepare tool arguments based on tool name
                    tool_args = self._prepare_tool_args(tool_name, query)

                    # Call the tool
                    result = await session.call_tool(tool_name, tool_args)

                    # Update last used
                    self.last_used = datetime.now().isoformat()

                    # Process result
                    if result and hasattr(result, 'content'):
                        content = result.content[0].text if result.content else str(result)
                    else:
                        content = str(result)

                    return {
                        "success": True,
                        "result": content,
                        "tool_used": tool_name,
                    "agent_type": "dynamic",
                    "agent_id": self.agent_id,
                    "server_id": self.server_id
                }
                
        except Exception as e:
            logger.error(f"Error querying agent {self.agent_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "agent_type": "dynamic",
                "agent_id": self.agent_id
            }
    
    def _prepare_tool_args(self, tool_name: str, query: str) -> Dict[str, Any]:
        """Prepare tool arguments based on tool name and query"""
        tool_name_lower = tool_name.lower()
        
        # Common argument patterns
        if 'search' in tool_name_lower:
            if 's3' in tool_name_lower or 'blob' in tool_name_lower or 'gcs' in tool_name_lower:
                return {"search_term": query}
            elif 'sharepoint' in tool_name_lower or 'onedrive' in tool_name_lower:
                return {"search_term": query}
            elif 'airbnb' in tool_name_lower:
                return {"location": query}
            elif 'google' in tool_name_lower:
                return {"query": query}
            else:
                return {"search_term": query}
        
        elif 'list' in tool_name_lower:
            if 's3' in tool_name_lower or 'blob' in tool_name_lower or 'gcs' in tool_name_lower:
                return {"prefix": query if len(query) < 50 else ""}
            else:
                return {}
        
        elif 'query' in tool_name_lower:
            if 'sql' in tool_name_lower or 'postgres' in tool_name_lower or 'mysql' in tool_name_lower:
                return {"sql_query": f"SELECT * FROM information_schema.tables WHERE table_name LIKE '%{query}%' LIMIT 10"}
            elif 'mongodb' in tool_name_lower:
                return {"collection_name": query, "query": "{}"}
            else:
                return {"query": query}
        
        elif 'get' in tool_name_lower:
            if 'weather' in tool_name_lower:
                return {"location": query}
            elif 'news' in tool_name_lower:
                return {"category": query if query in ['general', 'business', 'technology', 'sports'] else 'general'}
            else:
                return {"query": query}
        
        # Default fallback
        return {"query": query}
    
    def get_info(self) -> Dict[str, Any]:
        """Get agent information"""
        return {
            "agent_id": self.agent_id,
            "user_id": self.user_id,
            "server_id": self.server_id,
            "agent_name": self.agent_name,
            "transport": "stdio",
            "status": self.status,
            "created_at": self.created_at,
            "last_used": self.last_used
        }

    def cleanup(self):
        """Clean up agent resources"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            except Exception as e:
                logger.warning(f"Error cleaning up agent {self.agent_id}: {e}")
            finally:
                self.server_process = None

class DynamicAgentManager:
    """Manager for all dynamic agents"""
    
    def __init__(self):
        self.agents: Dict[str, DynamicAgent] = {}
        self.user_agents: Dict[str, List[str]] = {}  # user_id -> list of agent_ids
        logger.info("Dynamic Agent Manager initialized")
    
    async def create_agent(self, user_id: str, server_id: str, agent_name: str) -> str:
        """Create a new dynamic agent for a user's MCP server"""
        try:
            # Validate server_id
            server_info = get_dynamic_mcp_server_info(server_id)
            if not server_info:
                raise Exception(f"Server {server_id} not found")
            
            server_type = server_info.get("server_type")
            category = server_info.get("category")
            service = server_info.get("service")
            
            # Validate server type and category
            valid_categories = {
                "custom": ["cloud_storage", "databases", "devops", "git"],
                "public": ["locally_available"]
            }
            valid_services = {
                "cloud_storage": ["aws_s3", "gcs_drive", "microsoft_sharepoint", "azure_blob", "onedrive"],
                "databases": ["postgres", "mysql", "mongodb", "redis", "sql_server"],
                "devops": ["jira", "azure_devops"],
                "git": ["github", "bitbucket", "azure_repos"],
                "locally_available": ["airbnb", "news", "google", "weather"]
            }
            
            # Validate server type and category
            if server_type not in ["custom", "public"]:
                raise Exception(f"Invalid server type {server_type}")
        
            if not category or category not in valid_services:
                raise Exception(f"Invalid category {category} for server type {server_type}")
        
            if not service or service not in valid_services[category]:
                raise Exception(f"Invalid service {service} for category {category} in server type {server_type}")
            
            agent_id = f"agent_{uuid.uuid4().hex[:8]}"
            
            # Create agent
            agent = DynamicAgent(agent_id, user_id, server_id, agent_name)
            
            # Initialize agent
            if not await agent.initialize():
                raise Exception("Failed to initialize agent")
            
            # Store agent
            self.agents[agent_id] = agent
            
            # Track user's agents
            if user_id not in self.user_agents:
                self.user_agents[user_id] = []
            self.user_agents[user_id].append(agent_id)
            
            logger.info(f"Created dynamic agent {agent_id} for user {user_id} with server type {server_type} and service {service}")
            return agent_id
            
        except Exception as e:
            logger.error(f"Error creating agent for user {user_id}: {e}")
            raise
    
    async def query_user_agent(self, user_id: str, query: str) -> Dict[str, Any]:
        """Query a user's specialized agent"""
        try:
            # Get user's agents
            user_agent_ids = self.user_agents.get(user_id, [])
            
            if not user_agent_ids:
                return {
                    "success": False,
                    "error": "No specialized agents found for user",
                     "result": "",
                    "agent_type": "dynamic"
                }
            
            #Use the most recently created agent
            agent_id = user_agent_ids[-1]
            agent = self.agents.get(agent_id)
            
            if not agent:
                return {
                    "success": False,
                    "error": "Agent not found",
                    "result": "",
                    "agent_type": "dynamic"
                }
            
            #Query the agent
            return await agent.query(query)
            
        except Exception as e:
            logger.error(f"Error querying user agent for {user_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                 "agent_type": "dynamic"
            }
    
    def get_user_agents(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all agents for a user"""
        user_agent_ids = self.user_agents.get(user_id, [])
        return [self.agents[agent_id].get_info() for agent_id in user_agent_ids if agent_id in self.agents]
    
    def get_all_agents(self) -> List[Dict[str, Any]]:
        """Get all agents"""
        return [agent.get_info() for agent in self.agents.values()]
    
    async def delete_agent(self, agent_id: str) -> bool:
        """Delete a dynamic agent"""
        try:
            if agent_id not in self.agents:
                return False
            
            agent = self.agents[agent_id]
            user_id = agent.user_id
            
            # Remove from user's agents
            if user_id in self.user_agents:
                self.user_agents[user_id] = [aid for aid in self.user_agents[user_id] if aid != agent_id]
                if not self.user_agents[user_id]:
                    del self.user_agents[user_id]
            
            # Remove agent
            del self.agents[agent_id]
            
            logger.info(f"Deleted dynamic agent {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting agent {agent_id}: {e}")
            return False

# Global agent manager instance
dynamic_agent_manager = DynamicAgentManager()

# Functions for external use
async def create_dynamic_agent(user_id: str, server_id: str, agent_name: str) -> str:
    """Create a new dynamic agent"""
    return await dynamic_agent_manager.create_agent(user_id, server_id, agent_name)

async def query_user_dynamic_agent(user_id: str, query: str) -> str:
    """Query a user's dynamic agent"""
    result = await dynamic_agent_manager.query_user_agent(user_id, query)
    if result.get("success", False):
        return result.get("result", "")
    else:
        return f"Error: {result.get('error', 'Unknown error')}"

def get_user_dynamic_agents(user_id: str) -> List[Dict[str, Any]]:
    """Get all dynamic agents for a user"""
    return dynamic_agent_manager.get_user_agents(user_id)

def get_all_dynamic_agents() -> List[Dict[str, Any]]:
    """Get all dynamic agents"""
    return dynamic_agent_manager.get_all_agents()

async def delete_dynamic_agent(agent_id: str) -> bool:
    """Delete a dynamic agent"""
    return await dynamic_agent_manager.delete_agent(agent_id)