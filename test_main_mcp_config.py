#!/usr/bin/env python3
"""
Test script to verify the main11.py MCP configuration works
"""
import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_mcp_client_config():
    """Test the MCP client configuration that will be used in main11.py"""
    try:
        from langchain_mcp_adapters.client import MultiServerMCPClient
        
        print("🔍 Testing MultiServerMCPClient configuration...")
        
        # Get the absolute path to the qdrant server script (same as main11.py)
        server_script_path = Path(__file__).parent / "server" / "qdrant.py"
        print(f"📁 Server script path: {server_script_path}")
        print(f"📁 Script exists: {server_script_path.exists()}")
        
        if not server_script_path.exists():
            print("❌ Qdrant server script not found!")
            return False
        
        # Test the configuration
        mcp_client = MultiServerMCPClient(
            {
                "qdrant": {
                    "command": "python",
                    "args": [str(server_script_path)],
                    "transport": "stdio"
                }
            }
        )
        print("✅ MCP client initialized successfully")
        
        # Test getting tools
        print("🔧 Testing tool retrieval...")
        tools = await mcp_client.get_tools()
        print(f"✅ Available tools: {[tool.name for tool in tools]}")
        
        # Look for qdrant_find tool
        qdrant_tool = next((tool for tool in tools if tool.name == "qdrant_find"), None)
        if not qdrant_tool:
            print("❌ qdrant_find tool not found!")
            return False
        
        print("✅ qdrant_find tool found!")
        
        # Test a simple query
        print("🧪 Testing simple query...")
        test_result = await qdrant_tool.coroutine(
            query="test", 
            collection_name=os.getenv("COLLECTION_NAME", "Gen AI")
        )
        print("✅ Test query successful!")
        print(f"📊 Result preview: {str(test_result)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🔍 Testing main11.py MCP Configuration")
    print("=" * 60)
    
    success = await test_mcp_client_config()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Configuration test PASSED - main11.py should work!")
    else:
        print("❌ Configuration test FAILED - needs fixing")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
