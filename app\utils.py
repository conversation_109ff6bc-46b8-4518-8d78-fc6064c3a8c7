import re
from typing import Optional

def handle_greeting(query_text: str) -> Optional[str]:
    # Check for name introduction first
    name_match = re.search(r"\b(my name is|i'm|i am)\s+([a-zA-Z]+)", query_text, re.IGNORECASE)
    if name_match:
        name = name_match.group(2).capitalize()
        return f"Hello, {name}! How can I help you today?"

    # Prioritize specific greetings before generic ones
    greetings_patterns = [
        (r"\bgood morning\b", "Good morning! How can I assist you today?"),
        (r"\bgood afternoon\b", "Good afternoon! How can I assist you today?"),
        (r"\bgood evening\b", "Good evening! How can I assist you today?"),
        (r"\bgood night\b", "Good night! Take care, Sweet dreams..."),
        (r"\bhow are you\b|\bhow's it going\b|\bhow have you been\b", "I'm doing great, thanks for asking! How can I assist you today?"),
        (r"\bwhat's up\b|\bsup\b|\byo\b", "Not much, just here to help! How can I assist you today?"),
        (r"\blong time no see\b", "It's great to hear from you again! How can I assist you today?"),
        (r"\bthank you\b|\bthanks\b", "You're welcome! Let me know if there's anything else I can help with."),
        (r"\bbye\b|\bgoodbye\b|\bsee you\b|\btake care\b", "Take care! Feel free to reach out anytime."),
        (r"\bhave a (great|nice|good) day\b", "Thank you! Wishing you the same."),
        (r"\bsee you later\b", "See you later! Have a great day."),
        (r"\bcya\b|\bcatch you later\b", "Catch you later!"),
        (r"\bhi\b|\bhello\b|\bhey\b|\bhola\b", "Hello! How can I help you today?"), 
    ]

    for pattern, response in greetings_patterns:
        if re.search(pattern, query_text, re.IGNORECASE):
            return response

    return None



