#!/usr/bin/env python3
"""
Memory Manager - Short-term (Redis) and Long-term (MongoDB) Memory
Provides context management for the Multi-Agent RAG system
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import redis
import ssl
from pymongo import MongoClient
from bson.objectid import ObjectId
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MemoryManager")

class MemoryManager:
    """
    Memory manager that handles both short-term (Redis) and long-term (MongoDB) memory
    Integrates with existing Redis and MongoDB setup from main9.py
    """
    
    def __init__(self, redis_client=None, mongo_client=None, mongo_db=None, mongo_collection=None):
        # Redis configuration
        self.redis_client = redis_client
        self.session_ttl = int(os.getenv("SESSION_TTL", 900))  # 15 minutes
        
        # MongoDB configuration
        self.mongo_client = mongo_client
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        
        logger.info("Memory Manager initialized")
    
    def get_short_term_context(self, user_id: str, chat_id: str, limit: int = 10) -> List[Dict]:
        """
        Get short-term context from Redis
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of recent messages from Redis
        """
        if not self.redis_client:
            logger.debug("Redis not available, returning empty context")
            return []
        
        try:
            session_key = f"session:{user_id}:{chat_id}"
            messages = self.redis_client.lrange(session_key, -limit, -1)
            
            if messages:
                # Extend TTL since we're accessing the session
                self.redis_client.expire(session_key, self.session_ttl)
                logger.debug(f"Retrieved {len(messages)} messages from Redis for session {session_key}")
                return [json.loads(msg) for msg in messages]
            
            logger.debug(f"No messages found in Redis for session {session_key}")
            return []
            
        except redis.ConnectionError as e:
            logger.error(f"Redis connection error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error retrieving short-term context: {e}")
            return []
    
    def get_long_term_context(self, chat_id: str, limit: int = 20) -> List[Dict]:
        """
        Get long-term context from MongoDB

        Args:
            chat_id: Chat session identifier
            limit: Maximum number of messages to retrieve

        Returns:
            List of messages from MongoDB
        """
        if self.mongo_collection is None:
            logger.debug("MongoDB not available, returning empty context")
            return []
        
        try:
            chat_id_obj = ObjectId(chat_id)
            session_doc = self.mongo_collection.find_one({
                "$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}]
            })
            
            if not session_doc:
                logger.debug(f"No session found in MongoDB for chat_id {chat_id}")
                return []
            
            messages = session_doc.get('messages', [])
            # Return the most recent messages up to the limit
            return messages[-limit:] if len(messages) > limit else messages
            
        except Exception as e:
            logger.error(f"Error retrieving long-term context: {e}")
            return []
    
    def get_context(self, chat_id: str, user_id: str, limit: int = 10) -> str:
        """
        Get context with strict session isolation - UPDATED
        
        Args:
            chat_id: Chat session identifier
            user_id: User identifier
            limit: Maximum number of messages
        
        Returns:
            Session-specific formatted context string
        """
        try:
            # Use session-specific context retrieval
            session_messages = self.get_session_specific_context(user_id, chat_id, limit)
            
            if session_messages:
                logger.debug(f"Using session-specific context: {len(session_messages)} messages")
                context_messages = session_messages
            else:
                # NO fallback to MongoDB for context - keep sessions isolated
                logger.debug(f"No session context found for {chat_id}, returning empty context")
                return ""
            
            # Format context for LLM
            if not context_messages:
                return ""
            
            formatted_context = []
            for msg in context_messages:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                if role and content:
                    formatted_context.append(f"{role.capitalize()}: {content}")
            
            return "\n".join(formatted_context)
            
        except Exception as e:
            logger.error(f"Error getting session context: {e}")
            return ""

    def list_user_sessions(self, user_id: str) -> List[Dict]:
        """
        List all active sessions for a user
        
        Args:
            user_id: User identifier
        
        Returns:
            List of active sessions with metadata
        """
        if not self.redis_client:
            return []
        
        try:
            # Find all session metadata keys for this user
            pattern = f"session_meta:{user_id}:*"
            session_keys = self.redis_client.keys(pattern)
            
            sessions = []
            for key in session_keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                chat_id = key_str.split(':')[-1]
                
                metadata = self.redis_client.hgetall(key)
                if metadata:
                    sessions.append({
                        'chat_id': chat_id,
                        'user_id': user_id,
                        'metadata': metadata,
                        'session_key': f"session:{user_id}:{chat_id}"
                    })
            
            logger.debug(f"Found {len(sessions)} active sessions for user {user_id}")
            return sessions
            
        except Exception as e:
            logger.error(f"Error listing user sessions: {e}")
            return []
    
    def store_short_term_message(self, user_id: str, chat_id: str, message: Dict):
        """
        Store message in short-term memory (Redis)
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            message: Message dictionary
        """
        if not self.redis_client:
            logger.debug("Redis not available, skipping short-term storage")
            return
        
        try:
            session_key = f"session:{user_id}:{chat_id}"
            
            # Add timestamp if not present
            if 'timestamp' not in message:
                message['timestamp'] = datetime.now().isoformat()
            
            # Store message
            self.redis_client.rpush(session_key, json.dumps(message))
            self.redis_client.expire(session_key, self.session_ttl)
            
            # Keep only last 10 messages to manage memory
            self.redis_client.ltrim(session_key, -10, -1)
            
            logger.debug(f"Stored message in Redis for session {session_key}")
            
        except redis.ConnectionError as e:
            logger.error(f"Redis connection error during storage: {e}")
        except Exception as e:
            logger.error(f"Error storing short-term message: {e}")
    
    def store_session_metadata(self, user_id: str, chat_id: str, metadata: Dict):
        """
        Store session metadata in Redis
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            metadata: Metadata dictionary
        """
        if not self.redis_client:
            logger.debug("Redis not available, skipping metadata storage")
            return
        
        try:
            metadata_key = f"session_meta:{user_id}:{chat_id}"
            metadata_with_timestamp = metadata.copy()
            metadata_with_timestamp['updated_at'] = datetime.now().isoformat()
            
            # Store each metadata field
            for key, value in metadata_with_timestamp.items():
                self.redis_client.hset(metadata_key, key, str(value))
            
            self.redis_client.expire(metadata_key, self.session_ttl)
            logger.debug(f"Stored session metadata in Redis for {metadata_key}")
            
        except Exception as e:
            logger.error(f"Error storing session metadata: {e}")
    
    def get_session_metadata(self, user_id: str, chat_id: str) -> Dict:
        """
        Get session metadata from Redis
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            
        Returns:
            Session metadata dictionary
        """
        if not self.redis_client:
            logger.debug("Redis not available, returning empty metadata")
            return {}
        
        try:
            metadata_key = f"session_meta:{user_id}:{chat_id}"
            metadata = self.redis_client.hgetall(metadata_key)
            
            if metadata:
                # Extend TTL since we're accessing the metadata
                self.redis_client.expire(metadata_key, self.session_ttl)
                logger.debug(f"Retrieved session metadata from Redis for {metadata_key}")
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error retrieving session metadata: {e}")
            return {}
    
    def store_interaction(self, query: str, response: str, chat_id: str, user_id: str, 
                         metadata: Dict = None):
        """
        Store complete interaction in both short-term and long-term memory
        
        Args:
            query: User query
            response: System response
            chat_id: Chat session identifier
            user_id: User identifier
            metadata: Additional metadata
        """
        current_time = datetime.now().isoformat()
        
        # Create user message
        user_message = {
            "id": str(ObjectId()),
            "role": "user",
            "content": query,
            "timestamp": current_time,
            "metadata": metadata or {}
        }
        
        # Create assistant message
        assistant_message = {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": response,
            "timestamp": current_time,
            "metadata": metadata or {}
        }
        
        # Store in short-term memory (Redis)
        self.store_short_term_message(user_id, chat_id, user_message)
        self.store_short_term_message(user_id, chat_id, assistant_message)
        
        # Store in long-term memory (MongoDB) - this is handled by main9.py
        logger.debug(f"Interaction stored for chat_id {chat_id}")
    
    def clear_session(self, user_id: str, chat_id: str):
        """
        Clear session from short-term memory
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
        """
        if not self.redis_client:
            logger.debug("Redis not available, skipping session cleanup")
            return
        
        try:
            session_key = f"session:{user_id}:{chat_id}"
            metadata_key = f"session_meta:{user_id}:{chat_id}"
            
            deleted_count = self.redis_client.delete(session_key, metadata_key)
            logger.info(f"Cleared {deleted_count} keys from Redis: {session_key} and {metadata_key}")
            
        except Exception as e:
            logger.error(f"Error clearing session: {e}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        Get memory usage statistics

        Returns:
            Memory statistics dictionary
        """
        try:
            stats = {
                "redis_available": self.redis_client is not None,
                "mongodb_available": self.mongo_collection is not None,
                "session_ttl": self.session_ttl,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            # Fallback stats if there's an issue with collection comparison
            stats = {
                "redis_available": False,
                "mongodb_available": False,
                "session_ttl": self.session_ttl,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        
        if self.redis_client:
            try:
                info = self.redis_client.info()
                stats["redis_stats"] = {
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", "N/A"),
                    "total_commands_processed": info.get("total_commands_processed", "N/A")
                }
            except Exception as e:
                stats["redis_error"] = str(e)
        
        return stats

    def ensure_session_isolation(self, user_id: str, chat_id: str) -> bool:
        """
        Ensure session exists and is properly isolated
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
        
        Returns:
            True if session is valid and isolated
        """
        if not self.redis_client:
            return False
        
        try:
            session_key = f"session:{user_id}:{chat_id}"
            
            # Check if session exists
            exists = self.redis_client.exists(session_key)
            
            if not exists:
                # Initialize new session with metadata
                session_metadata = {
                    "created_at": datetime.now().isoformat(),
                    "user_id": user_id,
                    "chat_id": chat_id,
                    "message_count": 0,
                    "last_activity": datetime.now().isoformat()
                }
                self.store_session_metadata(user_id, chat_id, session_metadata)
                logger.info(f"Initialized new isolated session: {session_key}")
            
            # Update last activity
            self.redis_client.hset(f"session_meta:{user_id}:{chat_id}", 
                                  "last_activity", datetime.now().isoformat())
            
            return True
        
        except Exception as e:
            logger.error(f"Error ensuring session isolation: {e}")
            return False

    def get_session_specific_context(self, user_id: str, chat_id: str, limit: int = 10) -> List[Dict]:
        """
        Get context ONLY from the specific session - no cross-contamination
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            limit: Maximum number of messages
        
        Returns:
            Session-specific context only
        """
        # Ensure session isolation first
        if not self.ensure_session_isolation(user_id, chat_id):
            return []
        
        try:
            session_key = f"session:{user_id}:{chat_id}"
            
            # Get messages ONLY from this specific session
            messages = self.redis_client.lrange(session_key, -limit, -1)
            
            if messages:
                self.redis_client.expire(session_key, self.session_ttl)
                
                # Parse and validate messages belong to this session
                session_messages = []
                for msg in messages:
                    parsed_msg = json.loads(msg)
                    
                    # Validate message belongs to this session
                    if (parsed_msg.get('metadata', {}).get('chat_id') == chat_id or
                        not parsed_msg.get('metadata', {}).get('chat_id')):
                        session_messages.append(parsed_msg)
                    else:
                        logger.warning(f"Found message from different session in {session_key}")
            
                logger.debug(f"Retrieved {len(session_messages)} session-specific messages")
                return session_messages
        
            return []
        
        except Exception as e:
            logger.error(f"Error getting session-specific context: {e}")
            return []

    def store_session_message_isolated(self, user_id: str, chat_id: str, message: Dict):
        """
        Store message with strict session isolation
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            message: Message to store
        """
        if not self.redis_client:
            logger.debug("Redis not available")
            return
        
        try:
            # Ensure session isolation
            if not self.ensure_session_isolation(user_id, chat_id):
                logger.error(f"Failed to ensure session isolation for {user_id}:{chat_id}")
                return
            
            session_key = f"session:{user_id}:{chat_id}"
            
            # Add session metadata to message
            message_with_session = message.copy()
            if 'metadata' not in message_with_session:
                message_with_session['metadata'] = {}
            
            message_with_session['metadata'].update({
                'chat_id': chat_id,
                'user_id': user_id,
                'session_key': session_key,
                'stored_at': datetime.now().isoformat()
            })
            
            if 'timestamp' not in message_with_session:
                message_with_session['timestamp'] = datetime.now().isoformat()
            
            # Store message in session-specific key
            self.redis_client.rpush(session_key, json.dumps(message_with_session))
            self.redis_client.expire(session_key, self.session_ttl)
            
            # Keep only last 10 messages per session
            self.redis_client.ltrim(session_key, -10, -1)
            
            # Update session metadata
            self.redis_client.hincrby(f"session_meta:{user_id}:{chat_id}", "message_count", 1)
            self.redis_client.hset(f"session_meta:{user_id}:{chat_id}", 
                                  "last_activity", datetime.now().isoformat())
            
            logger.debug(f"Stored isolated message in session {session_key}")
            
        except Exception as e:
            logger.error(f"Error storing isolated session message: {e}")

# Global memory manager instance
memory_manager = None

def get_memory_manager(redis_client=None, mongo_client=None, mongo_db=None,
                      mongo_collection=None) -> MemoryManager:
    """Get or create memory manager instance"""
    global memory_manager
    if memory_manager is None:
        memory_manager = MemoryManager(redis_client, mongo_client, mongo_db, mongo_collection)
    else:
        # Update connections if provided
        if redis_client is not None:
            memory_manager.redis_client = redis_client
        if mongo_collection is not None:
            memory_manager.mongo_collection = mongo_collection
    return memory_manager

if __name__ == "__main__":
    # Test the memory manager
    print("🧠 Memory Manager Test")
    print("=" * 50)
    
    # Create test instance
    manager = get_memory_manager()
    
    # Test memory stats
    stats = manager.get_memory_stats()
    print(f"Redis available: {stats['redis_available']}")
    print(f"MongoDB available: {stats['mongodb_available']}")
    print(f"Session TTL: {stats['session_ttl']} seconds")
    
    # Test context retrieval (will be empty without actual connections)
    test_chat_id = "507f1f77bcf86cd799439011"
    test_user_id = "user123"
    
    context = manager.get_context(test_chat_id, test_user_id)
    print(f"Context length: {len(context)} characters")
    
    print("Memory Manager test completed")
