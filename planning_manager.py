#!/usr/bin/env python3
"""
Planning Manager - ReACT and CoT Planning Components
Implements reasoning and planning for the Multi-Agent RAG system
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("PlanningManager")

class PlanningManager:
    """
    Planning manager that implements ReACT (Reasoning + Acting) and 
    Chain of Thought (CoT) planning for the aggregator agent
    """
    
    def __init__(self):
        self.planning_history = []
        logger.info("Planning Manager initialized")
    
    def analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """
        Analyze query to determine intent and planning strategy
        
        Args:
            query: User query string
            
        Returns:
            Intent analysis with recommended strategy
        """
        query_lower = query.lower().strip()
        
        # Intent patterns
        intent_patterns = {
            "factual": [
                r"\bwho is\b", r"\bwhat is\b", r"\bwhen was\b", r"\bwhere is\b",
                r"\bhow old\b", r"\bdefine\b", r"\bexplain\b", r"\bdescribe\b"
            ],
            "current_events": [
                r"\blatest\b", r"\bnews\b", r"\brecent\b", r"\btoday\b",
                r"\bcurrent\b", r"\bnow\b", r"\bthis year\b", r"\b2024\b", r"\b2025\b"
            ],
            "comparison": [
                r"\bcompare\b", r"\bdifference\b", r"\bvs\b", r"\bversus\b",
                r"\bbetter\b", r"\bworse\b", r"\badvantages\b", r"\bdisadvantages\b"
            ],
            "how_to": [
                r"\bhow to\b", r"\bsteps\b", r"\bprocess\b", r"\bmethod\b",
                r"\bguide\b", r"\btutorial\b", r"\binstructions\b"
            ],
            "technical": [
                r"\bcode\b", r"\bprogramming\b", r"\balgorithm\b", r"\bapi\b",
                r"\bdatabase\b", r"\bsql\b", r"\bpython\b", r"\bjavascript\b"
            ]
        }
        
        # Analyze intent
        detected_intents = []
        for intent, patterns in intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    detected_intents.append(intent)
                    break
        
        # Determine primary intent
        primary_intent = detected_intents[0] if detected_intents else "general"
        
        # Query complexity analysis
        complexity = self._analyze_complexity(query)
        
        # Recommended strategy based on intent
        strategy = self._recommend_strategy(primary_intent, complexity, query_lower)
        
        analysis = {
            "primary_intent": primary_intent,
            "detected_intents": detected_intents,
            "complexity": complexity,
            "recommended_strategy": strategy,
            "query_length": len(query.split()),
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Query intent analysis: {primary_intent} ({complexity} complexity)")
        return analysis
    
    def _analyze_complexity(self, query: str) -> str:
        """Analyze query complexity"""
        words = query.split()
        
        # Simple heuristics for complexity
        if len(words) <= 3:
            return "simple"
        elif len(words) <= 8:
            return "medium"
        else:
            return "complex"
    
    def _recommend_strategy(self, intent: str, complexity: str, query_lower: str) -> Dict[str, Any]:
        """Recommend strategy based on intent and complexity"""
        
        # Strategy recommendations
        if intent == "current_events" or "latest" in query_lower or "news" in query_lower:
            return {
                "mode": "web",
                "reasoning": "Current events require web search for latest information",
                "agent_sequence": ["serper"],
                "fallback": None
            }
        
        elif intent == "factual" and complexity == "simple":
            return {
                "mode": "rag",
                "reasoning": "Simple factual queries can be answered from stored knowledge",
                "agent_sequence": ["qdrant"],
                "fallback": "web"
            }
        
        elif intent in ["technical", "how_to"] or complexity == "complex":
            return {
                "mode": "agentic",
                "reasoning": "Complex queries benefit from both stored knowledge and web search",
                "agent_sequence": ["qdrant", "serper"],
                "fallback": None
            }
        
        else:
            return {
                "mode": "agentic",
                "reasoning": "Default agentic approach for balanced results",
                "agent_sequence": ["qdrant", "serper"],
                "fallback": None
            }
    
    def create_execution_plan(self, query: str, mode: str = None, 
                            collections: List[str] = None) -> Dict[str, Any]:
        """
        Create detailed execution plan using ReACT methodology
        
        Args:
            query: User query
            mode: Execution mode (rag, agentic, web)
            collections: Qdrant collections to search
            
        Returns:
            Detailed execution plan
        """
        # Step 1: Analyze query intent
        intent_analysis = self.analyze_query_intent(query)
        
        # Step 2: Determine mode (override if provided)
        if mode:
            execution_mode = mode
            reasoning = f"Mode explicitly set to {mode}"
        else:
            execution_mode = intent_analysis["recommended_strategy"]["mode"]
            reasoning = intent_analysis["recommended_strategy"]["reasoning"]
        
        # Step 3: Create execution steps
        execution_steps = self._create_execution_steps(execution_mode, intent_analysis)
        
        # Step 4: Create plan
        plan = {
            "query": query,
            "mode": execution_mode,
            "intent_analysis": intent_analysis,
            "reasoning": reasoning,
            "execution_steps": execution_steps,
            "collections": collections or ["Gen AI"],
            "estimated_duration": self._estimate_duration(execution_steps),
            "created_at": datetime.now().isoformat(),
            "plan_id": f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        # Store in planning history
        self.planning_history.append(plan)
        
        logger.info(f"Created execution plan: {execution_mode} mode with {len(execution_steps)} steps")
        return plan
    
    def _create_execution_steps(self, mode: str, intent_analysis: Dict) -> List[Dict[str, Any]]:
        """Create detailed execution steps"""
        steps = []
        
        if mode == "rag":
            steps = [
                {
                    "step": 1,
                    "action": "query_qdrant",
                    "description": "Search Qdrant vector database for relevant information",
                    "agent": "qdrant",
                    "expected_output": "Relevant documents with similarity scores"
                },
                {
                    "step": 2,
                    "action": "evaluate_relevance",
                    "description": "Check if Qdrant results are sufficient",
                    "agent": "aggregator",
                    "expected_output": "Relevance decision"
                },
                {
                    "step": 3,
                    "action": "generate_response",
                    "description": "Generate final response from Qdrant results or fallback message",
                    "agent": "generative_model",
                    "expected_output": "Final answer"
                }
            ]
        
        elif mode == "web":
            steps = [
                {
                    "step": 1,
                    "action": "query_serper",
                    "description": "Search web using Google Search API",
                    "agent": "serper",
                    "expected_output": "Web search results with URLs"
                },
                {
                    "step": 2,
                    "action": "generate_response",
                    "description": "Generate final response from web search results",
                    "agent": "generative_model",
                    "expected_output": "Final answer"
                }
            ]
        
        elif mode == "agentic":
            steps = [
                {
                    "step": 1,
                    "action": "query_qdrant",
                    "description": "Search Qdrant vector database first",
                    "agent": "qdrant",
                    "expected_output": "Relevant documents with similarity scores"
                },
                {
                    "step": 2,
                    "action": "evaluate_relevance",
                    "description": "Check if Qdrant results are sufficient (threshold: 0.2)",
                    "agent": "aggregator",
                    "expected_output": "Relevance decision"
                },
                {
                    "step": 3,
                    "action": "conditional_web_search",
                    "description": "Query web search if Qdrant results insufficient",
                    "agent": "serper",
                    "expected_output": "Web search results (if needed)",
                    "condition": "qdrant_score < 0.2"
                },
                {
                    "step": 4,
                    "action": "combine_context",
                    "description": "Combine Qdrant and web results if both available",
                    "agent": "aggregator",
                    "expected_output": "Combined context"
                },
                {
                    "step": 5,
                    "action": "generate_response",
                    "description": "Generate final response from combined context",
                    "agent": "generative_model",
                    "expected_output": "Final answer"
                }
            ]
        
        return steps
    
    def _estimate_duration(self, steps: List[Dict]) -> float:
        """Estimate execution duration in seconds"""
        # Simple estimation based on step types
        duration_map = {
            "query_qdrant": 2.0,
            "query_serper": 3.0,
            "evaluate_relevance": 0.5,
            "combine_context": 0.5,
            "generate_response": 4.0,
            "conditional_web_search": 3.0
        }
        
        total_duration = 0.0
        for step in steps:
            action = step.get("action", "")
            total_duration += duration_map.get(action, 1.0)
        
        return total_duration
    
    def chain_of_thought_reasoning(self, query: str, context: Dict) -> Dict[str, Any]:
        """
        Implement Chain of Thought reasoning for decision making
        
        Args:
            query: User query
            context: Current context including scores, results, etc.
            
        Returns:
            Reasoning chain with decisions
        """
        reasoning_chain = {
            "query": query,
            "thoughts": [],
            "decisions": [],
            "final_reasoning": "",
            "timestamp": datetime.now().isoformat()
        }
        
        # Step 1: Analyze current context with enhanced details
        qdrant_score = context.get("score", 0.0)
        mode = context.get("mode", "agentic")
        documents = context.get("documents", [])
        collections_searched = context.get("collections_searched", [])
        source = context.get("source", "unknown")

        reasoning_chain["thoughts"].append(f"Query: '{query}'")
        reasoning_chain["thoughts"].append(f"Mode: {mode}")
        reasoning_chain["thoughts"].append(f"Qdrant relevance score: {qdrant_score:.3f}")
        reasoning_chain["thoughts"].append(f"Collections searched: {collections_searched}")
        reasoning_chain["thoughts"].append(f"Documents found: {len(documents)}")
        reasoning_chain["thoughts"].append(f"Source: {source}")

        # Add document details if available
        if documents:
            for i, doc in enumerate(documents[:3]):  # Show first 3 documents
                file_name = doc.get("file", "unknown")
                collection = doc.get("collection", "unknown")
                doc_score = doc.get("score", 0.0)
                reasoning_chain["thoughts"].append(f"  Document {i+1}: {file_name} (Collection: {collection}, Score: {doc_score:.3f})")

        # Step 2: Enhanced mode-specific reasoning
        if mode == "rag":
            # More lenient threshold for RAG mode - any documents with score > 0 should be considered
            if documents and len(documents) > 0:
                # Check if we have any meaningful content
                has_content = any(doc.get("content_preview", "").strip() for doc in documents)
                if has_content or qdrant_score > 0.01:
                    decision = "Use Qdrant results"
                    reasoning = f"RAG mode with documents found (score: {qdrant_score:.3f}, {len(documents)} docs)"
                else:
                    decision = "Return fallback message"
                    reasoning = f"RAG mode with documents but no meaningful content (score: {qdrant_score:.3f}, {len(documents)} docs)"
            else:
                decision = "Return fallback message"
                reasoning = f"RAG mode but no documents found (score: {qdrant_score:.3f})"

            reasoning_chain["thoughts"].append(f"RAG mode: {'Documents found' if documents else 'No documents'} ({len(documents)} docs, score: {qdrant_score:.3f})")
            reasoning_chain["decisions"].append({"decision": decision, "reasoning": reasoning})
        
        elif mode == "web":
            decision = "Use web search only"
            reasoning = "Web mode - skip Qdrant, use web search directly for real-time information"
            reasoning_chain["thoughts"].append("Web mode: Direct web search (bypassing Qdrant)")
            reasoning_chain["thoughts"].append("Web mode rationale: Real-time information needed")
            reasoning_chain["decisions"].append({"decision": decision, "reasoning": reasoning})
        
        elif mode == "agentic":
            # Enhanced agentic mode reasoning with better thresholds
            if qdrant_score >= 0.2 and documents:
                decision = "Use Qdrant results only"
                reasoning = f"Agentic mode with high relevance - Qdrant sufficient (score: {qdrant_score:.3f}, {len(documents)} docs)"
            elif qdrant_score >= 0.05 and documents:
                decision = "Use Qdrant results with caution"
                reasoning = f"Agentic mode with moderate relevance - using Qdrant (score: {qdrant_score:.3f}, {len(documents)} docs)"
            else:
                decision = "Use web search"
                reasoning = f"Agentic mode with low relevance - need web search fallback (score: {qdrant_score:.3f}, {len(documents)} docs)"

            threshold_category = "High" if qdrant_score >= 0.2 else ("Moderate" if qdrant_score >= 0.05 else "Low")
            reasoning_chain["thoughts"].append(f"Agentic mode: {threshold_category} relevance (threshold: 0.3 for high, 0.05 for moderate)")
            reasoning_chain["decisions"].append({"decision": decision, "reasoning": reasoning})
        
        # Step 3: Enhanced final reasoning summary
        final_decision = reasoning_chain["decisions"][-1] if reasoning_chain["decisions"] else {}
        decision_text = final_decision.get("decision", "No decision made")
        reasoning_text = final_decision.get("reasoning", "No reasoning provided")

        # Create comprehensive final reasoning
        reasoning_chain["final_reasoning"] = f"{reasoning_text}"

        # Add summary statistics
        reasoning_chain["summary"] = {
            "mode": mode,
            "score": qdrant_score,
            "documents_found": len(documents),
            "collections_searched": len(collections_searched),
            "decision": decision_text,
            "source_used": source
        }

        logger.info(f"CoT reasoning: {decision_text} | {reasoning_text}")
        return reasoning_chain
    
    def get_planning_history(self, limit: int = 10) -> List[Dict]:
        """Get recent planning history"""
        return self.planning_history[-limit:] if self.planning_history else []
    
    def clear_planning_history(self):
        """Clear planning history"""
        self.planning_history.clear()
        logger.info("Planning history cleared")

# Global planning manager instance
planning_manager = None

def get_planning_manager() -> PlanningManager:
    """Get or create planning manager instance"""
    global planning_manager
    if planning_manager is None:
        planning_manager = PlanningManager()
    return planning_manager

def test_planning_manager():
    """Test the planning manager with comprehensive scenarios"""
    print("📋 PLANNING MANAGER TEST")
    print("=" * 60)

    try:
        # Initialize planning manager
        planner = get_planning_manager()
        print("✅ Planning manager initialized")

        # Test queries with different intents and complexities
        test_queries = [
            {
                "query": "Who is Sundar Pichai?",
                "expected_mode": "rag",
                "description": "Simple factual query"
            },
            {
                "query": "Latest news about artificial intelligence in 2024",
                "expected_mode": "web",
                "description": "Current events query"
            },
            {
                "query": "How to implement a neural network in Python with TensorFlow?",
                "expected_mode": "agentic",
                "description": "Complex technical how-to query"
            },
            {
                "query": "Compare Python and JavaScript for web development",
                "expected_mode": "agentic",
                "description": "Comparison query"
            },
            {
                "query": "What is machine learning?",
                "expected_mode": "rag",
                "description": "Simple definition query"
            },
            {
                "query": "Recent developments in quantum computing research",
                "expected_mode": "web",
                "description": "Recent research query"
            }
        ]

        print(f"\n🧪 Testing {len(test_queries)} different query types...")

        for i, test_case in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}: {test_case['description']}")
            print(f"Query: '{test_case['query']}'")
            print(f"Expected mode: {test_case['expected_mode']}")
            print("-" * 40)

            try:
                # Test 1: Query intent analysis
                analysis = planner.analyze_query_intent(test_case['query'])
                print(f"✅ Intent analysis:")
                print(f"   Primary intent: {analysis['primary_intent']}")
                print(f"   Detected intents: {analysis['detected_intents']}")
                print(f"   Complexity: {analysis['complexity']}")
                print(f"   Query length: {analysis['query_length']} words")

                # Test 2: Execution plan creation
                plan = planner.create_execution_plan(test_case['query'])
                print(f"✅ Execution plan:")
                print(f"   Recommended mode: {plan['mode']}")
                print(f"   Execution steps: {len(plan['execution_steps'])}")
                print(f"   Estimated duration: {plan['estimated_duration']:.1f}s")
                print(f"   Reasoning: {plan['reasoning']}")
                print(f"   Plan ID: {plan['plan_id']}")

                # Show execution steps
                print(f"   Steps breakdown:")
                for step in plan['execution_steps']:
                    step_desc = f"     {step['step']}. {step['action']} ({step['agent']})"
                    if 'condition' in step:
                        step_desc += f" [if {step['condition']}]"
                    print(step_desc)

                # Test 3: Chain of Thought reasoning
                mock_context = {
                    "score": 0.5,
                    "mode": plan['mode']
                }
                reasoning = planner.chain_of_thought_reasoning(test_case['query'], mock_context)
                print(f"✅ CoT reasoning:")
                print(f"   Thoughts: {len(reasoning['thoughts'])} steps")
                print(f"   Decisions: {len(reasoning['decisions'])}")
                print(f"   Final reasoning: {reasoning['final_reasoning']}")

                # Verify mode recommendation
                mode_match = "✅" if plan['mode'] == test_case['expected_mode'] else "⚠️"
                print(f"{mode_match} Mode prediction: Expected {test_case['expected_mode']}, Got {plan['mode']}")

            except Exception as e:
                print(f"❌ Test failed: {str(e)}")

        # Test 4: Planning history
        print(f"\n📚 Test: Planning History")
        print("-" * 30)
        history = planner.get_planning_history(limit=3)
        print(f"✅ Planning history: {len(history)} recent plans")

        if history:
            latest_plan = history[-1]
            print(f"✅ Latest plan: {latest_plan['mode']} mode for '{latest_plan['query'][:50]}...'")

        # Test 5: Different mode overrides
        print(f"\n🔄 Test: Mode Override")
        print("-" * 30)
        test_query = "What is artificial intelligence?"

        for mode in ["rag", "agentic", "web"]:
            plan = planner.create_execution_plan(test_query, mode=mode)
            print(f"✅ Override to {mode}: {len(plan['execution_steps'])} steps, {plan['estimated_duration']:.1f}s")

        # Test 6: Clear history
        print(f"\n🧹 Test: Clear Planning History")
        print("-" * 30)
        planner.clear_planning_history()
        history_after = planner.get_planning_history()
        print(f"✅ History cleared: {len(history_after)} plans remaining")

        print("\n" + "=" * 60)
        print("🎉 Planning Manager testing completed!")
        print("📊 Summary:")
        print(f"   - Tested {len(test_queries)} different query types")
        print(f"   - Analyzed intents, created plans, and applied CoT reasoning")
        print(f"   - All planning components working correctly")

    except Exception as e:
        print(f"❌ Planning manager test failed: {str(e)}")
        print("💡 Make sure dependencies are installed:")
        print("   pip install python-dotenv")

if __name__ == "__main__":
    print("🚀 Starting Planning Manager Test...")
    test_planning_manager()
