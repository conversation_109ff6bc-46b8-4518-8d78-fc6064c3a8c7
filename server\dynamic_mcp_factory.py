#!/usr/bin/env python3
"""
Dynamic MCP Server Factory
Creates and manages dynamic MCP servers for various services
"""
import asyncio
import json
import logging
import os
import subprocess
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import tempfile
import shutil
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DynamicMCPFactory")

class DynamicMCPFactory:
    """Factory for creating and managing dynamic MCP servers"""
    
    def __init__(self):
        self.servers: Dict[str, Dict] = {}
        self.processes: Dict[str, subprocess.Popen] = {}
        self.base_port = 8100  # Start from 8100 to avoid conflict with main app and other services
        self.server_dir = Path("dynamic_servers")
        self.server_dir.mkdir(exist_ok=True)
        logger.info("Dynamic MCP Factory initialized")
    
    def _get_next_port(self) -> int:
        """Get next available port for MCP server"""
        used_ports = [info.get("port", 0) for info in self.servers.values()]
        port = self.base_port
        while port in used_ports:
            port += 1
        return port
    
    def _generate_server_id(self) -> str:
        """Generate unique server ID"""
        return f"mcp_{uuid.uuid4().hex[:8]}"

    def _get_category_from_service(self, service: str) -> str:
        """Determine category from service type"""
        service_to_category = {
            # Cloud storage services
            "aws_s3": "cloud_storage",
            "gcs_drive": "cloud_storage",
            "microsoft_sharepoint": "cloud_storage",
            "azure_blob": "cloud_storage",
            "onedrive": "cloud_storage",

            # Database services
            "postgres": "databases",
            "mysql": "databases",
            "mongodb": "databases",
            "redis": "databases",
            "sql_server": "databases",
            "sqlite": "databases",

            # DevOps services
            "jira": "devops",
            "azure_devops": "devops",
            "github_actions": "devops",
            "gitlab_ci": "devops",
            "jenkins": "devops",

            # Git services
            "github": "git",
            "gitlab": "git",
            "bitbucket": "git",
            "azure_repos": "git",

            # Public services
            "airbnb": "locally_available",
            "weather": "locally_available",
            "news": "locally_available",
            "google": "locally_available"
        }

        return service_to_category.get(service, "unknown")
    
    async def create_server(self, server_type: str, service: str, credentials: Dict[str, Any],
                          server_name: str, description: str = "", user_id: str = None) -> Dict[str, Any]:
        """Create a new dynamic MCP server"""
        try:
            server_id = self._generate_server_id()

            # Determine category from service
            category = self._get_category_from_service(service)

            # Create server directory
            server_path = self.server_dir / server_id
            server_path.mkdir(exist_ok=True)

            # Generate server configuration
            config = {
                "server_id": server_id,
                "server_name": server_name,
                "server_type": server_type,
                "service": service,
                "category": category,
                "credentials": credentials,
                "description": description,
                "transport": "stdio",
                "status": "created",
                "created_at": datetime.now().isoformat(),
                "last_used": None,
                "running": False,
                "user_id": user_id
            }
            
            # Generate server code based on service type
            server_code = self._generate_server_code(config)
            
            # Write server file
            server_file = server_path / f"{server_id}.py"
            with open(server_file, 'w') as f:
                f.write(server_code)
            
            # Store server info
            self.servers[server_id] = config
            
            logger.info(f"Created dynamic MCP server: {server_id} for {service}")
            return {
                "server_id": server_id,
                "user_id": user_id,
                "transport": "stdio"
            }
            
        except Exception as e:
            logger.error(f"Error creating MCP server: {e}")
            raise
    
    def _generate_server_code(self, config: Dict) -> str:
        """Generate MCP server code based on service type"""
        service = config["service"]
        
        if service == "aws_s3":
            return self._generate_aws_s3_server(config)
        elif service == "gcs_drive":
            return self._generate_gcs_server(config)
        elif service == "microsoft_sharepoint":
            return self._generate_sharepoint_server(config)
        elif service == "onedrive":
            return self._generate_onedrive_server(config)
        elif service == "azure_blob":
            return self._generate_azure_blob_server(config)
        elif service in ["postgres", "mysql", "mongodb", "redis", "sql_server", "sqlite"]:
            return self._generate_database_server(config)
        elif service in ["jira", "azure_devops", "github_actions", "gitlab_ci", "jenkins"]:
            return self._generate_devops_server(config)
        elif service in ["github", "gitlab", "bitbucket", "azure_repos"]:
            return self._generate_git_server(config)
        elif service in ["airbnb", "weather", "news", "google"]:
            return self._generate_public_server(config)
        else:
            raise ValueError(f"Unsupported service: {service}")
    
    def _generate_aws_s3_server(self, config: Dict) -> str:
        """Generate AWS S3 MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]
        
        return f'''#!/usr/bin/env python3
"""
Dynamic AWS S3 MCP Server - {server_id}
"""
import logging
import boto3
import os
from mcp.server.fastmcp import FastMCP
from botocore.exceptions import ClientError, NoCredentialsError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AWS_S3_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("aws_s3_{server_id}")

# AWS S3 Configuration
AWS_ACCESS_KEY_ID = "{credentials.get('aws_access_key_id', '')}"
AWS_SECRET_ACCESS_KEY = "{credentials.get('aws_secret_access_key', '')}"
BUCKET_NAME = "{credentials.get('bucket_name', '')}"
REGION = "{credentials.get('region', 'us-east-1')}"
SESSION_TOKEN = "{credentials.get('session_token', '')}"

# Initialize S3 client
try:
    session_kwargs = {{
        'aws_access_key_id': AWS_ACCESS_KEY_ID,
        'aws_secret_access_key': AWS_SECRET_ACCESS_KEY,
        'region_name': REGION
    }}
    if SESSION_TOKEN:
        session_kwargs['aws_session_token'] = SESSION_TOKEN
    
    s3_client = boto3.client('s3', **session_kwargs)
    logger.info("AWS S3 client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize S3 client: {{e}}")
    s3_client = None

@mcp.tool()
def list_s3_objects(prefix: str = "") -> str:
    """List objects in S3 bucket"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        
        response = s3_client.list_objects_v2(
            Bucket=BUCKET_NAME,
            Prefix=prefix,
            MaxKeys=100
        )
        
        objects = response.get('Contents', [])
        if not objects:
            return f"No objects found in bucket {{BUCKET_NAME}} with prefix '{{prefix}}'"
        
        result = f"Objects in {{BUCKET_NAME}}:\\n"
        for obj in objects:
            result += f"- {{obj['Key']}} ({{obj['Size']}} bytes, {{obj['LastModified']}})\\n"
        
        return result
    except Exception as e:
        return f"Error listing S3 objects: {{str(e)}}"

@mcp.tool()
def get_s3_object_content(key: str) -> str:
    """Get content of S3 object"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=key)
        content = response['Body'].read().decode('utf-8')
        return f"Content of {{key}}:\\n{{content}}"
    except Exception as e:
        return f"Error getting S3 object content: {{str(e)}}"

@mcp.tool()
def search_s3_objects(search_term: str) -> str:
    """Search for objects in S3 bucket by name"""
    try:
        if not s3_client:
            return "S3 client not initialized"
        
        response = s3_client.list_objects_v2(Bucket=BUCKET_NAME)
        objects = response.get('Contents', [])
        
        matching_objects = [obj for obj in objects if search_term.lower() in obj['Key'].lower()]
        
        if not matching_objects:
            return f"No objects found matching '{{search_term}}'"
        
        result = f"Objects matching '{{search_term}}':\\n"
        for obj in matching_objects:
            result += f"- {{obj['Key']}} ({{obj['Size']}} bytes)\\n"
        
        return result
    except Exception as e:
        return f"Error searching S3 objects: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting AWS S3 MCP server")
    mcp.run()
'''
    
    def _generate_gcs_server(self, config: Dict) -> str:
        """Generate Google Cloud Storage MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]
        
        return f'''#!/usr/bin/env python3
"""
Dynamic Google Cloud Storage MCP Server - {server_id}
"""
import logging
import json
from google.cloud import storage
from google.oauth2 import service_account
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GCS_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("gcs_{server_id}")

# GCS Configuration
SERVICE_ACCOUNT_JSON ={credentials.get('service_account_key_json', '')}
BUCKET_NAME = "{credentials.get('bucket_name', '')}"
PROJECT_ID = "{credentials.get('project_id', '')}"

# Initialize GCS client
try:
    if SERVICE_ACCOUNT_JSON:
        service_account_info = json.loads(SERVICE_ACCOUNT_JSON)
        credentials_obj = service_account.Credentials.from_service_account_info(service_account_info)
        gcs_client = storage.Client(credentials=credentials_obj, project=PROJECT_ID)
    else:
        gcs_client = storage.Client(project=PROJECT_ID)
    
    bucket = gcs_client.bucket(BUCKET_NAME)
    logger.info("GCS client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize GCS client: {{e}}")
    gcs_client = None
    bucket = None

@mcp.tool()
def list_gcs_objects(prefix: str = "") -> str:
    """List objects in GCS bucket"""
    try:
        if not bucket:
            return "GCS client not initialized"
        
        blobs = list(bucket.list_blobs(prefix=prefix, max_results=100))
        
        if not blobs:
            return f"No objects found in bucket {{BUCKET_NAME}} with prefix '{{prefix}}'"
        
        result = f"Objects in {{BUCKET_NAME}}:\\n"
        for blob in blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes, {{blob.time_created}})\\n"
        
        return result
    except Exception as e:
        return f"Error listing GCS objects: {{str(e)}}"

@mcp.tool()
def get_gcs_object_content(object_name: str) -> str:
    """Get content of GCS object"""
    try:
        if not bucket:
            return "GCS client not initialized"
        
        blob = bucket.blob(object_name)
        content = blob.download_as_text()
        return f"Content of {{object_name}}:\\n{{content}}"
    except Exception as e:
        return f"Error getting GCS object content: {{str(e)}}"

@mcp.tool()
def search_gcs_objects(search_term: str) -> str:
    """Search for objects in GCS bucket by name"""
    try:
        if not bucket:
            return "GCS client not initialized"
        
        blobs = list(bucket.list_blobs())
        matching_blobs = [blob for blob in blobs if search_term.lower() in blob.name.lower()]
        
        if not matching_blobs:
            return f"No objects found matching '{{search_term}}'"
        
        result = f"Objects matching '{{search_term}}':\\n"
        for blob in matching_blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes)\\n"
        
        return result
    except Exception as e:
        return f"Error searching GCS objects: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting GCS MCP server")
    mcp.run()
'''

    def _generate_sharepoint_server(self, config: Dict) -> str:
        """Generate Microsoft SharePoint MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Microsoft SharePoint MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SharePoint_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("sharepoint_{server_id}")

# SharePoint Configuration
TENANT_ID = "{credentials.get('tenant_id', '')}"
CLIENT_ID = "{credentials.get('client_id', '')}"
CLIENT_SECRET = "{credentials.get('client_secret', '')}"
SHAREPOINT_SITE_URL = "{credentials.get('sharepoint_site_url', '')}"
RESOURCE_DRIVE_ID = "{credentials.get('resource_drive_id', '')}"

@mcp.tool()
def list_sharepoint_files(folder_path: str = "") -> str:
    """List files in SharePoint site"""
    try:
        # Get access token
        token_url = f"https://login.microsoftonline.com/{{TENANT_ID}}/oauth2/v2.0/token"
        token_data = {{
            'grant_type': 'client_credentials',
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'scope': 'https://graph.microsoft.com/.default'
        }}

        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        access_token = token_response.json()['access_token']

        # List files
        headers = {{'Authorization': f'Bearer {{access_token}}'}}
        if RESOURCE_DRIVE_ID:
            api_url = f"https://graph.microsoft.com/v1.0/drives/{{RESOURCE_DRIVE_ID}}/root/children"
        else:
            site_id = SHAREPOINT_SITE_URL.split('/')[-1]
            api_url = f"https://graph.microsoft.com/v1.0/sites/{{site_id}}/drive/root/children"

        response = requests.get(api_url, headers=headers)
        response.raise_for_status()

        files = response.json().get('value', [])
        if not files:
            return "No files found in SharePoint site"

        result = "SharePoint Files:\\n"
        for file in files:
            result += f"- {{file['name']}} ({{file.get('size', 'N/A')}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error listing SharePoint files: {{str(e)}}"

@mcp.tool()
def search_sharepoint_files(search_term: str) -> str:
    """Search for files in SharePoint"""
    try:
        # Get access token
        token_url = f"https://login.microsoftonline.com/{{TENANT_ID}}/oauth2/v2.0/token"
        token_data = {{
            'grant_type': 'client_credentials',
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'scope': 'https://graph.microsoft.com/.default'
        }}

        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        access_token = token_response.json()['access_token']

        # Search files
        headers = {{'Authorization': f'Bearer {{access_token}}'}}
        search_url = f"https://graph.microsoft.com/v1.0/search/query"
        search_data = {{
            "requests": [{{
                "entityTypes": ["driveItem"],
                "query": {{
                    "queryString": search_term
                }}
            }}]
        }}

        response = requests.post(search_url, headers=headers, json=search_data)
        response.raise_for_status()

        results = response.json().get('value', [{{}}])[0].get('hitsContainers', [{{}}])[0].get('hits', [])

        if not results:
            return f"No files found matching '{{search_term}}'"

        result = f"Files matching '{{search_term}}':\\n"
        for hit in results:
            resource = hit.get('resource', {{}})
            result += f"- {{resource.get('name', 'Unknown')}}\\n"

        return result
    except Exception as e:
        return f"Error searching SharePoint files: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting SharePoint MCP server")
    mcp.run()
'''

    def _generate_onedrive_server(self, config: Dict) -> str:
        """Generate OneDrive MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic OneDrive MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("OneDrive_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("onedrive_{server_id}")

# OneDrive Configuration
TENANT_ID = "{credentials.get('tenant_id', '')}"
CLIENT_ID = "{credentials.get('client_id', '')}"
CLIENT_SECRET = "{credentials.get('client_secret', '')}"
REFRESH_TOKEN = "{credentials.get('refresh_token', '')}"

@mcp.tool()
def list_onedrive_files(folder_path: str = "") -> str:
    """List files in OneDrive"""
    try:
        # Get access token
        token_url = f"https://login.microsoftonline.com/{{TENANT_ID}}/oauth2/v2.0/token"
        token_data = {{
            'grant_type': 'refresh_token',
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'refresh_token': REFRESH_TOKEN,
            'scope': 'https://graph.microsoft.com/Files.ReadWrite'
        }}

        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        access_token = token_response.json()['access_token']

        # List files
        headers = {{'Authorization': f'Bearer {{access_token}}'}}
        api_url = "https://graph.microsoft.com/v1.0/me/drive/root/children"

        response = requests.get(api_url, headers=headers)
        response.raise_for_status()

        files = response.json().get('value', [])
        if not files:
            return "No files found in OneDrive"

        result = "OneDrive Files:\\n"
        for file in files:
            result += f"- {{file['name']}} ({{file.get('size', 'N/A')}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error listing OneDrive files: {{str(e)}}"

@mcp.tool()
def search_onedrive_files(search_term: str) -> str:
    """Search for files in OneDrive"""
    try:
        # Get access token
        token_url = f"https://login.microsoftonline.com/{{TENANT_ID}}/oauth2/v2.0/token"
        token_data = {{
            'grant_type': 'refresh_token',
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'refresh_token': REFRESH_TOKEN,
            'scope': 'https://graph.microsoft.com/Files.ReadWrite'
        }}

        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        access_token = token_response.json()['access_token']

        # Search files
        headers = {{'Authorization': f'Bearer {{access_token}}'}}
        search_url = f"https://graph.microsoft.com/v1.0/me/drive/search(q='{{search_term}}')"

        response = requests.get(search_url, headers=headers)
        response.raise_for_status()

        files = response.json().get('value', [])

        if not files:
            return f"No files found matching '{{search_term}}'"

        result = f"Files matching '{{search_term}}':\\n"
        for file in files:
            result += f"- {{file['name']}} ({{file.get('size', 'N/A')}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error searching OneDrive files: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting OneDrive MCP server")
    mcp.run()
'''

    def _generate_azure_blob_server(self, config: Dict) -> str:
        """Generate Azure Blob Storage MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Azure Blob Storage MCP Server - {server_id}
"""
import logging
from azure.storage.blob import BlobServiceClient
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AzureBlob_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("azure_blob_{server_id}")

# Azure Blob Configuration
STORAGE_ACCOUNT_NAME = "{credentials.get('storage_account_name', '')}"
ACCESS_KEY = "{credentials.get('access_key', '')}"
CONTAINER_NAME = "{credentials.get('container_name', '')}"
ENDPOINT_URL = "{credentials.get('endpoint_url', f'https://{credentials.get("storage_account_name", "")}.blob.core.windows.net')}"

# Initialize Azure Blob client
try:
    blob_service_client = BlobServiceClient(
        account_url=ENDPOINT_URL,
        credential=ACCESS_KEY
    )
    container_client = blob_service_client.get_container_client(CONTAINER_NAME)
    logger.info("Azure Blob client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Azure Blob client: {{e}}")
    blob_service_client = None
    container_client = None

@mcp.tool()
def list_azure_blobs(prefix: str = "") -> str:
    """List blobs in Azure container"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"

        blobs = list(container_client.list_blobs(name_starts_with=prefix))

        if not blobs:
            return f"No blobs found in container {{CONTAINER_NAME}} with prefix '{{prefix}}'"

        result = f"Blobs in {{CONTAINER_NAME}}:\\n"
        for blob in blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes, {{blob.last_modified}})\\n"

        return result
    except Exception as e:
        return f"Error listing Azure blobs: {{str(e)}}"

@mcp.tool()
def get_azure_blob_content(blob_name: str) -> str:
    """Get content of Azure blob"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"

        blob_client = container_client.get_blob_client(blob_name)
        content = blob_client.download_blob().readall().decode('utf-8')
        return f"Content of {{blob_name}}:\\n{{content}}"
    except Exception as e:
        return f"Error getting Azure blob content: {{str(e)}}"

@mcp.tool()
def search_azure_blobs(search_term: str) -> str:
    """Search for blobs in Azure container by name"""
    try:
        if not container_client:
            return "Azure Blob client not initialized"

        blobs = list(container_client.list_blobs())
        matching_blobs = [blob for blob in blobs if search_term.lower() in blob.name.lower()]

        if not matching_blobs:
            return f"No blobs found matching '{{search_term}}'"

        result = f"Blobs matching '{{search_term}}':\\n"
        for blob in matching_blobs:
            result += f"- {{blob.name}} ({{blob.size}} bytes)\\n"

        return result
    except Exception as e:
        return f"Error searching Azure blobs: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Azure Blob MCP server")
    mcp.run()
'''

    def _generate_database_server(self, config: Dict) -> str:
        """Generate database MCP server code"""
        service = config["service"]
        server_id = config["server_id"]

        if service == "postgres":
            return self._generate_postgres_server(config)
        elif service == "mysql":
            return self._generate_mysql_server(config)
        elif service == "mongodb":
            return self._generate_mongodb_server(config)
        elif service == "redis":
            return self._generate_redis_server(config)
        elif service == "sql_server":
            return self._generate_sql_server_server(config)
        elif service == "sqlite":
            return self._generate_sqlite_server(config)
        else:
            raise ValueError(f"Unsupported database service: {{service}}")

    def _generate_postgres_server(self, config: Dict) -> str:
        """Generate PostgreSQL MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic PostgreSQL MCP Server - {server_id}
"""
import logging
import psycopg2
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("PostgreSQL_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("postgres_{server_id}")

# PostgreSQL Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 5432)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"

@mcp.tool()
def query_postgres(sql_query: str) -> str:
    """Execute SQL query on PostgreSQL database"""
    try:
        if CONNECTION_STRING:
            conn = psycopg2.connect(CONNECTION_STRING)
        else:
            conn = psycopg2.connect(
                host=HOST,
                port=PORT,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE_NAME
            )

        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing PostgreSQL query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_postgres_tables() -> str:
    """List all tables in PostgreSQL database"""
    try:
        if CONNECTION_STRING:
            conn = psycopg2.connect(CONNECTION_STRING)
        else:
            conn = psycopg2.connect(
                host=HOST,
                port=PORT,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE_NAME
            )

        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
        """)

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing PostgreSQL tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting PostgreSQL MCP server")
    mcp.run()
'''

    def _generate_mysql_server(self, config: Dict) -> str:
        """Generate MySQL MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic MySQL MCP Server - {server_id}
"""
import logging
import mysql.connector
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MySQL_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("mysql_{server_id}")

# MySQL Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 3306)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"

@mcp.tool()
def query_mysql(sql_query: str) -> str:
    """Execute SQL query on MySQL database"""
    try:
        conn = mysql.connector.connect(
            host=HOST,
            port=PORT,
            user=USERNAME,
            password=PASSWORD,
            database=DATABASE_NAME
        )

        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing MySQL query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_mysql_tables() -> str:
    """List all tables in MySQL database"""
    try:
        conn = mysql.connector.connect(
            host=HOST,
            port=PORT,
            user=USERNAME,
            password=PASSWORD,
            database=DATABASE_NAME
        )

        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing MySQL tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting MySQL MCP server")
    mcp.run()
'''

    def _generate_mongodb_server(self, config: Dict) -> str:
        """Generate MongoDB MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic MongoDB MCP Server - {server_id}
"""
import logging
from pymongo import MongoClient
from mcp.server.fastmcp import FastMCP
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MongoDB_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("mongodb_{server_id}")

# MongoDB Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 27017)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"

@mcp.tool()
def query_mongodb(collection_name: str, query: str = "{{}}", limit: int = 10) -> str:
    """Query MongoDB collection"""
    try:
        if CONNECTION_STRING:
            client = MongoClient(CONNECTION_STRING)
        else:
            if USERNAME and PASSWORD:
                client = MongoClient(f"mongodb://{{USERNAME}}:{{PASSWORD}}@{{HOST}}:{{PORT}}/{{DATABASE_NAME}}")
            else:
                client = MongoClient(f"mongodb://{{HOST}}:{{PORT}}")

        db = client[DATABASE_NAME]
        collection = db[collection_name]

        # Parse query string to dict
        query_dict = json.loads(query) if query != "{{}}" else {{}}

        results = list(collection.find(query_dict).limit(limit))

        if not results:
            return f"No documents found in collection {{collection_name}}"

        result = f"Documents from {{collection_name}}:\\n"
        for i, doc in enumerate(results, 1):
            # Convert ObjectId to string for JSON serialization
            doc['_id'] = str(doc['_id'])
            result += f"{{i}}. {{json.dumps(doc, indent=2)}}\\n"

        return result

    except Exception as e:
        return f"Error querying MongoDB: {{str(e)}}"
    finally:
        if 'client' in locals():
            client.close()

@mcp.tool()
def list_mongodb_collections() -> str:
    """List all collections in MongoDB database"""
    try:
        if CONNECTION_STRING:
            client = MongoClient(CONNECTION_STRING)
        else:
            if USERNAME and PASSWORD:
                client = MongoClient(f"mongodb://{{USERNAME}}:{{PASSWORD}}@{{HOST}}:{{PORT}}/{{DATABASE_NAME}}")
            else:
                client = MongoClient(f"mongodb://{{HOST}}:{{PORT}}")

        db = client[DATABASE_NAME]
        collections = db.list_collection_names()

        if not collections:
            return "No collections found in database"

        result = "Collections in database:\\n"
        for collection in collections:
            result += f"- {{collection}}\\n"

        return result

    except Exception as e:
        return f"Error listing MongoDB collections: {{str(e)}}"
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    logger.info("Starting MongoDB MCP server")
    mcp.run()
'''

    def _generate_redis_server(self, config: Dict) -> str:
        """Generate Redis MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Redis MCP Server - {server_id}
"""
import logging
import redis
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Redis_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("redis_{server_id}")

# Redis Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 6379)}
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NUM = {credentials.get('database_name', 0)}

@mcp.tool()
def redis_get(key: str) -> str:
    """Get value from Redis"""
    try:
        r = redis.Redis(
            host=HOST,
            port=PORT,
            password=PASSWORD if PASSWORD else None,
            db=DATABASE_NUM,
            decode_responses=True
        )

        value = r.get(key)
        if value is None:
            return f"Key '{{key}}' not found in Redis"

        return f"Value for '{{key}}': {{value}}"

    except Exception as e:
        return f"Error getting Redis key: {{str(e)}}"

@mcp.tool()
def redis_keys(pattern: str = "*") -> str:
    """List Redis keys matching pattern"""
    try:
        r = redis.Redis(
            host=HOST,
            port=PORT,
            password=PASSWORD if PASSWORD else None,
            db=DATABASE_NUM,
            decode_responses=True
        )

        keys = r.keys(pattern)

        if not keys:
            return f"No keys found matching pattern '{{pattern}}'"

        result = f"Keys matching '{{pattern}}':\\n"
        for key in keys:
            result += f"- {{key}}\\n"

        return result

    except Exception as e:
        return f"Error listing Redis keys: {{str(e)}}"

@mcp.tool()
def redis_info() -> str:
    """Get Redis server information"""
    try:
        r = redis.Redis(
            host=HOST,
            port=PORT,
            password=PASSWORD if PASSWORD else None,
            db=DATABASE_NUM,
            decode_responses=True
        )

        info = r.info()

        result = "Redis Server Info:\\n"
        result += f"Redis Version: {{info.get('redis_version', 'N/A')}}\\n"
        result += f"Connected Clients: {{info.get('connected_clients', 'N/A')}}\\n"
        result += f"Used Memory: {{info.get('used_memory_human', 'N/A')}}\\n"
        result += f"Total Keys: {{info.get('db0', {{}}).get('keys', 0) if 'db0' in info else 0}}\\n"

        return result

    except Exception as e:
        return f"Error getting Redis info: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Redis MCP server")
    mcp.run()
'''

    def _generate_sql_server_server(self, config: Dict) -> str:
        """Generate SQL Server MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic SQL Server MCP Server - {server_id}
"""
import logging
import pyodbc
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SQLServer_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("sqlserver_{server_id}")

# SQL Server Configuration
HOST = "{credentials.get('host', 'localhost')}"
PORT = {credentials.get('port', 1433)}
USERNAME = "{credentials.get('username', '')}"
PASSWORD = "{credentials.get('password', '')}"
DATABASE_NAME = "{credentials.get('database_name', '')}"
CONNECTION_STRING = "{credentials.get('connection_string', '')}"

@mcp.tool()
def query_sqlserver(sql_query: str) -> str:
    """Execute SQL query on SQL Server database"""
    try:
        if CONNECTION_STRING:
            conn = pyodbc.connect(CONNECTION_STRING)
        else:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={{HOST}},{{PORT}};DATABASE={{DATABASE_NAME}};UID={{USERNAME}};PWD={{PASSWORD}}"
            conn = pyodbc.connect(conn_str)

        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [column[0] for column in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing SQL Server query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_sqlserver_tables() -> str:
    """List all tables in SQL Server database"""
    try:
        if CONNECTION_STRING:
            conn = pyodbc.connect(CONNECTION_STRING)
        else:
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={{HOST}},{{PORT}};DATABASE={{DATABASE_NAME}};UID={{USERNAME}};PWD={{PASSWORD}}"
            conn = pyodbc.connect(conn_str)

        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_type = 'BASE TABLE'
        """)

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing SQL Server tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting SQL Server MCP server")
    mcp.run()
'''

    def _generate_sqlite_server(self, config: Dict) -> str:
        """Generate SQLite MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic SQLite MCP Server - {server_id}
"""
import logging
import sqlite3
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SQLite_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("sqlite_{server_id}")

# SQLite Configuration
DATABASE_PATH = "{credentials.get('database_name', 'database.db')}"

@mcp.tool()
def query_sqlite(sql_query: str) -> str:
    """Execute SQL query on SQLite database"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute(sql_query)

        if sql_query.strip().upper().startswith('SELECT'):
            results = cursor.fetchall()
            columns = [description[0] for description in cursor.description]

            result = f"Query Results:\\n"
            result += f"Columns: {{', '.join(columns)}}\\n"
            result += "-" * 50 + "\\n"

            for row in results:
                result += f"{{', '.join(str(val) for val in row)}}\\n"

            return result
        else:
            conn.commit()
            return f"Query executed successfully. Rows affected: {{cursor.rowcount}}"

    except Exception as e:
        return f"Error executing SQLite query: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def list_sqlite_tables() -> str:
    """List all tables in SQLite database"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")

        tables = cursor.fetchall()

        if not tables:
            return "No tables found in database"

        result = "Tables in database:\\n"
        for table in tables:
            result += f"- {{table[0]}}\\n"

        return result

    except Exception as e:
        return f"Error listing SQLite tables: {{str(e)}}"
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("Starting SQLite MCP server")
    mcp.run()
'''

    def _generate_devops_server(self, config: Dict) -> str:
        """Generate DevOps tools MCP server code"""
        service = config["service"]

        if service == "jira":
            return self._generate_jira_server(config)
        elif service == "azure_devops":
            return self._generate_azure_devops_server(config)
        elif service == "github_actions":
            return self._generate_github_actions_server(config)
        elif service == "gitlab_ci":
            return self._generate_gitlab_ci_server(config)
        elif service == "jenkins":
            return self._generate_jenkins_server(config)
        else:
            raise ValueError(f"Unsupported DevOps service: {service}")

    def _generate_jira_server(self, config: Dict) -> str:
        """Generate Jira MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic Jira MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP
import base64

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Jira_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("jira_{server_id}")

# Jira Configuration
API_URL = "{credentials.get('api_url', '')}"
USERNAME = "{credentials.get('username', '')}"
API_TOKEN = "{credentials.get('api_token', '')}"
PROJECT_KEY = "{credentials.get('project_key', '')}"

@mcp.tool()
def search_jira_issues(jql: str = "", max_results: int = 10) -> str:
    """Search Jira issues using JQL"""
    try:
        auth_string = base64.b64encode(f"{{USERNAME}}:{{API_TOKEN}}".encode()).decode()
        headers = {{
            'Authorization': f'Basic {{auth_string}}',
            'Content-Type': 'application/json'
        }}

        if not jql:
            jql = f"project = {{PROJECT_KEY}} ORDER BY created DESC" if PROJECT_KEY else "ORDER BY created DESC"

        url = f"{{API_URL}}/rest/api/3/search"
        params = {{
            'jql': jql,
            'maxResults': max_results,
            'fields': 'summary,status,assignee,created,priority'
        }}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        issues = data.get('issues', [])

        if not issues:
            return "No issues found"

        result = f"Jira Issues ({{len(issues)}} found):\\n"
        for issue in issues:
            fields = issue.get('fields', {{}})
            result += f"- {{issue['key']}}: {{fields.get('summary', 'No summary')}}\\n"
            result += f"  Status: {{fields.get('status', {{}}).get('name', 'Unknown')}}\\n"
            result += f"  Assignee: {{fields.get('assignee', {{}}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned'}}\\n"
            result += f"  Priority: {{fields.get('priority', {{}}).get('name', 'Unknown') if fields.get('priority') else 'Unknown'}}\\n\\n"

        return result

    except Exception as e:
        return f"Error searching Jira issues: {{str(e)}}"

@mcp.tool()
def get_jira_issue(issue_key: str) -> str:
    """Get details of a specific Jira issue"""
    try:
        auth_string = base64.b64encode(f"{{USERNAME}}:{{API_TOKEN}}".encode()).decode()
        headers = {{
            'Authorization': f'Basic {{auth_string}}',
            'Content-Type': 'application/json'
        }}

        url = f"{{API_URL}}/rest/api/3/issue/{{issue_key}}"

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        issue = response.json()
        fields = issue.get('fields', {{}})

        result = f"Jira Issue {{issue_key}}:\\n"
        result += f"Summary: {{fields.get('summary', 'No summary')}}\\n"
        result += f"Description: {{fields.get('description', 'No description')}}\\n"
        result += f"Status: {{fields.get('status', {{}}).get('name', 'Unknown')}}\\n"
        result += f"Assignee: {{fields.get('assignee', {{}}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned'}}\\n"
        result += f"Reporter: {{fields.get('reporter', {{}}).get('displayName', 'Unknown') if fields.get('reporter') else 'Unknown'}}\\n"
        result += f"Priority: {{fields.get('priority', {{}}).get('name', 'Unknown') if fields.get('priority') else 'Unknown'}}\\n"
        result += f"Created: {{fields.get('created', 'Unknown')}}\\n"

        return result

    except Exception as e:
        return f"Error getting Jira issue: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Jira MCP server")
    mcp.run()
'''

    def _generate_git_server(self, config: Dict) -> str:
        """Generate Git service MCP server code"""
        service = config["service"]

        if service == "github":
            return self._generate_github_server(config)
        elif service == "gitlab":
            return self._generate_gitlab_server(config)
        elif service == "bitbucket":
            return self._generate_bitbucket_server(config)
        elif service == "azure_repos":
            return self._generate_azure_repos_server(config)
        else:
            raise ValueError(f"Unsupported Git service: {service}")

    def _generate_github_server(self, config: Dict) -> str:
        """Generate GitHub MCP server code"""
        credentials = config["credentials"]
        server_id = config["server_id"]

        return f'''#!/usr/bin/env python3
"""
Dynamic GitHub MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GitHub_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("github_{server_id}")

# GitHub Configuration
USERNAME = "{credentials.get('username', '')}"
TOKEN = "{credentials.get('token', '')}"
REPOSITORY_URL = "{credentials.get('repository_url', '')}"
ORGANIZATION = "{credentials.get('organization', '')}"

@mcp.tool()
def list_github_repos(org: str = "") -> str:
    """List GitHub repositories"""
    try:
        headers = {{
            'Authorization': f'token {{TOKEN}}',
            'Accept': 'application/vnd.github.v3+json'
        }}

        if org or ORGANIZATION:
            url = f"https://api.github.com/orgs/{{org or ORGANIZATION}}/repos"
        else:
            url = f"https://api.github.com/users/{{USERNAME}}/repos"

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        repos = response.json()

        if not repos:
            return "No repositories found"

        result = f"GitHub Repositories ({{len(repos)}} found):\\n"
        for repo in repos:
            result += f"- {{repo['name']}} ({{repo['language'] or 'Unknown'}})\\n"
            result += f"  Description: {{repo['description'] or 'No description'}}\\n"
            result += f"  Stars: {{repo['stargazers_count']}} | Forks: {{repo['forks_count']}}\\n"
            result += f"  URL: {{repo['html_url']}}\\n\\n"

        return result

    except Exception as e:
        return f"Error listing GitHub repositories: {{str(e)}}"

@mcp.tool()
def search_github_code(query: str, repo: str = "") -> str:
    """Search code in GitHub repositories"""
    try:
        headers = {{
            'Authorization': f'token {{TOKEN}}',
            'Accept': 'application/vnd.github.v3+json'
        }}

        search_query = f"{{query}}"
        if repo:
            search_query += f" repo:{{repo}}"
        elif ORGANIZATION:
            search_query += f" org:{{ORGANIZATION}}"

        url = f"https://api.github.com/search/code"
        params = {{'q': search_query, 'per_page': 10}}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        items = data.get('items', [])

        if not items:
            return f"No code found matching '{{query}}'"

        result = f"GitHub Code Search Results for '{{query}}' ({{len(items)}} found):\\n"
        for item in items:
            result += f"- {{item['name']}} in {{item['repository']['full_name']}}\\n"
            result += f"  Path: {{item['path']}}\\n"
            result += f"  URL: {{item['html_url']}}\\n\\n"

        return result

    except Exception as e:
        return f"Error searching GitHub code: {{str(e)}}"

@mcp.tool()
def get_github_issues(repo: str, state: str = "open") -> str:
    """Get GitHub issues for a repository"""
    try:
        headers = {{
            'Authorization': f'token {{TOKEN}}',
            'Accept': 'application/vnd.github.v3+json'
        }}

        url = f"https://api.github.com/repos/{{repo}}/issues"
        params = {{'state': state, 'per_page': 10}}

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        issues = response.json()

        if not issues:
            return f"No {{state}} issues found in {{repo}}"

        result = f"GitHub Issues in {{repo}} ({{state}}) - {{len(issues)}} found:\\n"
        for issue in issues:
            result += f"- #{{issue['number']}}: {{issue['title']}}\\n"
            result += f"  State: {{issue['state']}} | Comments: {{issue['comments']}}\\n"
            result += f"  Created: {{issue['created_at']}}\\n"
            result += f"  URL: {{issue['html_url']}}\\n\\n"

        return result

    except Exception as e:
        return f"Error getting GitHub issues: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting GitHub MCP server")
    mcp.run()
'''

    # Placeholder methods for other services
    def _generate_azure_devops_server(self, config: Dict) -> str:
        """Generate Azure DevOps MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Azure DevOps MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AzureDevOps_MCP_{server_id}")

mcp = FastMCP("azure_devops_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Azure DevOps"""
    return "Azure DevOps MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Azure DevOps MCP server")
    mcp.run()
'''

    def _generate_github_actions_server(self, config: Dict) -> str:
        """Generate GitHub Actions MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic GitHub Actions MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GitHubActions_MCP_{server_id}")

mcp = FastMCP("github_actions_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for GitHub Actions"""
    return "GitHub Actions MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting GitHub Actions MCP server")
    mcp.run()
'''

    def _generate_gitlab_ci_server(self, config: Dict) -> str:
        """Generate GitLab CI MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic GitLab CI MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GitLabCI_MCP_{server_id}")

mcp = FastMCP("gitlab_ci_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for GitLab CI"""
    return "GitLab CI MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting GitLab CI MCP server")
    mcp.run()
'''

    def _generate_jenkins_server(self, config: Dict) -> str:
        """Generate Jenkins MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Jenkins MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Jenkins_MCP_{server_id}")

mcp = FastMCP("jenkins_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Jenkins"""
    return "Jenkins MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Jenkins MCP server")
    mcp.run()
'''

    def _generate_gitlab_server(self, config: Dict) -> str:
        """Generate GitLab MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic GitLab MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GitLab_MCP_{server_id}")

mcp = FastMCP("gitlab_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for GitLab"""
    return "GitLab MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting GitLab MCP server")
    mcp.run()
'''

    def _generate_bitbucket_server(self, config: Dict) -> str:
        """Generate Bitbucket MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Bitbucket MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Bitbucket_MCP_{server_id}")

mcp = FastMCP("bitbucket_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Bitbucket"""
    return "Bitbucket MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Bitbucket MCP server")
    mcp.run()
'''

    def _generate_azure_repos_server(self, config: Dict) -> str:
        """Generate Azure Repos MCP server code (placeholder)"""
        server_id = config["server_id"]
        return f'''#!/usr/bin/env python3
"""
Dynamic Azure Repos MCP Server - {server_id} (Placeholder)
"""
import logging
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AzureRepos_MCP_{server_id}")

mcp = FastMCP("azure_repos_{server_id}")

@mcp.tool()
def placeholder_tool() -> str:
    """Placeholder tool for Azure Repos"""
    return "Azure Repos MCP server placeholder - implementation needed"

if __name__ == "__main__":
    logger.info("Starting Azure Repos MCP server")
    mcp.run()
'''

    def _generate_public_server(self, config: Dict) -> str:
        """Generate public service MCP server code"""
        service = config["service"]
        server_id = config["server_id"]

        if service == "airbnb":
            return f'''#!/usr/bin/env python3
"""
Dynamic Airbnb MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Airbnb_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("airbnb_{server_id}")

@mcp.tool()
def search_airbnb_listings(location: str, checkin: str = "", checkout: str = "") -> str:
    """Search Airbnb listings (mock implementation)"""
    try:
        # This is a mock implementation - in real scenario, you'd use Airbnb API
        result = f"Mock Airbnb search results for {{location}}:\\n"
        result += f"1. Cozy apartment in downtown {{location}} - $120/night\\n"
        result += f"2. Modern studio near {{location}} center - $95/night\\n"
        result += f"3. Luxury villa in {{location}} suburbs - $250/night\\n"
        result += f"\\nNote: This is a mock implementation. Real implementation would require Airbnb API access."
        return result
    except Exception as e:
        return f"Error searching Airbnb listings: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Airbnb MCP server")
    mcp.run()
'''
        elif service == "weather":
            return f'''#!/usr/bin/env python3
"""
Dynamic Weather MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Weather_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("weather_{server_id}")

@mcp.tool()
def get_weather(location: str) -> str:
    """Get weather information for a location"""
    try:
        # Using OpenWeatherMap API (free tier)
        api_key = "demo_key"  # In real implementation, this would be from environment
        url = f"http://api.openweathermap.org/data/2.5/weather?q={{location}}&appid={{api_key}}&units=metric"

        # Mock response for demo
        result = f"Weather in {{location}}:\\n"
        result += f"Temperature: 22°C\\n"
        result += f"Condition: Partly cloudy\\n"
        result += f"Humidity: 65%\\n"
        result += f"Wind: 10 km/h\\n"
        result += f"\\nNote: This is a mock implementation. Real implementation would require OpenWeatherMap API key."
        return result
    except Exception as e:
        return f"Error getting weather: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Weather MCP server")
    mcp.run()
'''
        elif service == "news":
            return f'''#!/usr/bin/env python3
"""
Dynamic News MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("News_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("news_{server_id}")

@mcp.tool()
def get_latest_news(category: str = "general") -> str:
    """Get latest news"""
    try:
        # Mock news results
        result = f"Latest {{category}} news:\\n"
        result += f"1. Breaking: Major development in AI technology\\n"
        result += f"2. Global markets show positive trends\\n"
        result += f"3. Climate summit reaches new agreements\\n"
        result += f"\\nNote: This is a mock implementation. Real implementation would require News API key."
        return result
    except Exception as e:
        return f"Error getting news: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting News MCP server")
    mcp.run()
'''
        elif service == "google":
            return f'''#!/usr/bin/env python3
"""
Dynamic Google MCP Server - {server_id}
"""
import logging
import requests
from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Google_MCP_{server_id}")

# Initialize MCP server
mcp = FastMCP("google_{server_id}")

@mcp.tool()
def google_search(query: str) -> str:
    """Perform Google search (mock implementation)"""
    try:
        # Mock search results
        result = f"Google search results for '{{query}}':\\n"
        result += f"1. Wikipedia article about {{query}}\\n"
        result += f"2. Official website related to {{query}}\\n"
        result += f"3. News articles about {{query}}\\n"
        result += f"\\nNote: This is a mock implementation. Real implementation would require Google Custom Search API."
        return result
    except Exception as e:
        return f"Error performing Google search: {{str(e)}}"

if __name__ == "__main__":
    logger.info("Starting Google MCP server")
    mcp.run()
'''
        else:
            raise ValueError(f"Unsupported public service: {{service}}")

    async def start_server(self, server_id: str) -> bool:
        """Start a dynamic MCP server"""
        try:
            if server_id not in self.servers:
                logger.error(f"Server {{server_id}} not found")
                return False

            config = self.servers[server_id]
            if config.get("running", False):
                logger.info(f"Server {{server_id}} is already running")
                return True

            # Start the server process
            server_file = self.server_dir / server_id / f"{{server_id}}.py"

            if not server_file.exists():
                logger.error(f"Server file not found: {{server_file}}")
                return False

            # Start server process
            process = subprocess.Popen([
                "python", str(server_file)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Store process
            self.processes[server_id] = process

            # Wait a moment for the server to start
            await asyncio.sleep(2)

            # Check if process is still running
            if process.poll() is not None:
                # Process has terminated, check for errors
                stdout, stderr = process.communicate()
                logger.error(f"Server {{server_id}} failed to start. STDOUT: {{stdout.decode()}}, STDERR: {{stderr.decode()}}")
                return False

            # Update server status
            config["status"] = "running"
            config["running"] = True
            config["last_used"] = datetime.now().isoformat()

            logger.info(f"Started dynamic MCP server: {{server_id}} with stdio transport")
            return True

        except Exception as e:
            logger.error(f"Error starting server {{server_id}}: {{e}}")
            return False

    async def stop_server(self, server_id: str) -> bool:
        """Stop a dynamic MCP server"""
        try:
            if server_id not in self.servers:
                logger.error(f"Server {{server_id}} not found")
                return False

            # Stop process if running
            if server_id in self.processes:
                process = self.processes[server_id]
                process.terminate()
                process.wait(timeout=5)
                del self.processes[server_id]

            # Update server status
            config = self.servers[server_id]
            config["status"] = "stopped"
            config["running"] = False

            logger.info(f"Stopped dynamic MCP server: {{server_id}}")
            return True

        except Exception as e:
            logger.error(f"Error stopping server {{server_id}}: {{e}}")
            return False

    async def delete_server(self, server_id: str) -> bool:
        """Delete a dynamic MCP server"""
        try:
            # Stop server first
            await self.stop_server(server_id)

            # Remove server directory
            server_path = self.server_dir / server_id
            if server_path.exists():
                shutil.rmtree(server_path)

            # Remove from servers dict
            if server_id in self.servers:
                del self.servers[server_id]

            logger.info(f"Deleted dynamic MCP server: {{server_id}}")
            return True

        except Exception as e:
            logger.error(f"Error deleting server {{server_id}}: {{e}}")
            return False

    def list_servers(self) -> List[Dict]:
        """List all dynamic MCP servers"""
        return list(self.servers.values())

    def get_server_info(self, server_id: str) -> Optional[Dict]:
        """Get information about a specific server"""
        return self.servers.get(server_id)

    def get_server_url(self, server_id: str) -> Optional[str]:
        """Get the URL of a specific server"""
        if server_id in self.servers:
            return self.servers[server_id].get("url")
        return None

# Global factory instance
dynamic_mcp_factory = DynamicMCPFactory()

# Factory functions for external use
async def create_dynamic_mcp_server(server_type: str, service: str, credentials: Dict[str, Any],
                                  server_name: str, description: str = "", user_id: str = None) -> Dict[str, Any]:
    """Create a new dynamic MCP server"""
    return await dynamic_mcp_factory.create_server(
        server_type, service, credentials, server_name, description, user_id
    )

async def start_dynamic_mcp_server(server_id: str) -> bool:
    """Start a dynamic MCP server"""
    return await dynamic_mcp_factory.start_server(server_id)

async def stop_dynamic_mcp_server(server_id: str) -> bool:
    """Stop a dynamic MCP server"""
    return await dynamic_mcp_factory.stop_server(server_id)

async def delete_dynamic_mcp_server(server_id: str) -> bool:
    """Delete a dynamic MCP server"""
    return await dynamic_mcp_factory.delete_server(server_id)

def list_dynamic_mcp_servers() -> List[Dict]:
    """List all dynamic MCP servers"""
    return dynamic_mcp_factory.list_servers()

def get_dynamic_mcp_server_info(server_id: str) -> Optional[Dict]:
    """Get information about a specific server"""
    return dynamic_mcp_factory.get_server_info(server_id)

def get_dynamic_mcp_server_url(server_id: str) -> Optional[str]:
    """Get the URL of a specific server"""
    return dynamic_mcp_factory.get_server_url(server_id)
