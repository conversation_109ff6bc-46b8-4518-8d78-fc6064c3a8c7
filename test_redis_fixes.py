#!/usr/bin/env python3
"""
Test script to verify Redis duplication fixes and feedback system
"""
import sys
import os
import json
import redis
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_redis_connection():
    """Test Redis connection"""
    try:
        # Use the same Redis configuration as main11.py
        redis_client = redis.Redis(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", 6379)),
            password=os.getenv("REDIS_PASSWORD"),
            db=int(os.getenv("REDIS_DB", 0)),
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10
        )
        
        response = redis_client.ping()
        print(f"✅ Redis connection successful: PING = {response}")
        return redis_client
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return None

def simulate_conversation_storage(redis_client, user_id="test_user", chat_id="test_chat"):
    """Simulate storing a conversation to test duplication prevention"""
    if not redis_client:
        print("❌ Redis not available for testing")
        return
    
    print("\n🧪 Testing Conversation Storage:")
    print("=" * 50)
    
    session_key = f"session:{user_id}:{chat_id}"
    
    # Clear any existing test data
    redis_client.delete(session_key)
    
    # Simulate first query storage
    query = "What is machine learning?"
    response = "Machine learning is a subset of artificial intelligence..."
    
    user_message = {
        "id": "test_user_msg_1",
        "role": "user",
        "content": query,
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "chat_id": chat_id,
            "mode": "rag",
            "host": "groq",
            "model": "llama-3.3-70b-versatile"
        }
    }
    
    assistant_message = {
        "id": "test_assistant_msg_1",
        "role": "assistant",
        "content": response,
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "chat_id": chat_id,
            "sources": ["document1.pdf"],
            "source_urls": [],
            "mode": "rag",
            "host": "groq",
            "model": "llama-3.3-70b-versatile"
        }
    }
    
    # Store messages
    redis_client.rpush(session_key, json.dumps(user_message))
    redis_client.rpush(session_key, json.dumps(assistant_message))
    redis_client.expire(session_key, 3600)
    
    # Check storage
    messages = redis_client.lrange(session_key, 0, -1)
    print(f"✅ Stored {len(messages)} messages")
    
    # Test duplication prevention (try to store same query again)
    existing_messages = [json.loads(msg) for msg in messages]
    duplicate_found = False
    for i, msg in enumerate(existing_messages):
        if (msg.get('role') == 'user' and 
            msg.get('content') == query and
            i + 1 < len(existing_messages) and
            existing_messages[i + 1].get('role') == 'assistant'):
            duplicate_found = True
            break
    
    if duplicate_found:
        print("✅ Duplication prevention working - would skip duplicate storage")
    else:
        print("❌ Duplication prevention not working")
    
    return session_key

def test_feedback_system(redis_client, user_id="test_user", chat_id="test_chat", session_key=None):
    """Test feedback storage and retrieval"""
    if not redis_client or not session_key:
        print("❌ Redis or session key not available for feedback testing")
        return
    
    print("\n🔄 Testing Feedback System:")
    print("=" * 50)
    
    # Get current messages
    all_msgs = redis_client.lrange(session_key, 0, -1)
    all_messages = [json.loads(m) for m in all_msgs]
    
    # Find last assistant message
    last_assistant_idx = -1
    for i in range(len(all_messages) - 1, -1, -1):
        if all_messages[i].get('role') == 'assistant':
            last_assistant_idx = i
            break
    
    if last_assistant_idx == -1:
        print("❌ No assistant message found for feedback")
        return
    
    # Add feedback
    feedback_entry = {
        "feedback_type": "thumbs-up",
        "feedback_value": "True",
        "timestamp": datetime.now().isoformat(),
    }
    
    # Attach feedback to assistant message
    all_messages[last_assistant_idx]["feedback"] = feedback_entry
    
    # Update session
    redis_client.delete(session_key)
    for msg in all_messages:
        redis_client.rpush(session_key, json.dumps(msg))
    redis_client.expire(session_key, 3600)
    
    # Store feedback for analytics
    feedback_key = f"feedback:{user_id}:{chat_id}"
    feedback_with_context = {
        **feedback_entry,
        "query": all_messages[last_assistant_idx - 1].get('content') if last_assistant_idx > 0 else "",
        "response": all_messages[last_assistant_idx].get('content', ""),
        "metadata": all_messages[last_assistant_idx].get('metadata', {})
    }
    redis_client.rpush(feedback_key, json.dumps(feedback_with_context))
    redis_client.expire(feedback_key, 3600)
    
    # Verify feedback storage
    updated_messages = redis_client.lrange(session_key, 0, -1)
    updated_parsed = [json.loads(m) for m in updated_messages]
    
    feedback_found = False
    for msg in updated_parsed:
        if msg.get('role') == 'assistant' and 'feedback' in msg:
            feedback_found = True
            print(f"✅ Feedback attached to assistant message: {msg['feedback']['feedback_type']}")
            break
    
    if not feedback_found:
        print("❌ Feedback not found in assistant message")
    
    # Test feedback context retrieval
    feedback_entries = redis_client.lrange(feedback_key, 0, -1)
    if feedback_entries:
        print(f"✅ Feedback stored for analytics: {len(feedback_entries)} entries")
        for entry_str in feedback_entries:
            entry = json.loads(entry_str)
            print(f"   - Type: {entry.get('feedback_type')}, Value: {entry.get('feedback_value')}")
    else:
        print("❌ No feedback entries found for analytics")

def test_feedback_context_generation(redis_client, user_id="test_user", chat_id="test_chat"):
    """Test feedback context generation for improving responses"""
    if not redis_client:
        print("❌ Redis not available for feedback context testing")
        return
    
    print("\n📈 Testing Feedback Context Generation:")
    print("=" * 50)
    
    feedback_key = f"feedback:{user_id}:{chat_id}"
    feedback_entries = redis_client.lrange(feedback_key, -5, -1)
    
    if not feedback_entries:
        print("❌ No feedback entries found")
        return
    
    feedback_context = []
    for entry_str in feedback_entries:
        entry = json.loads(entry_str)
        feedback_type = entry.get('feedback_type', 'unknown')
        feedback_value = entry.get('feedback_value', 'unknown')
        query = entry.get('query', '')[:50]  # Truncate for display
        
        if feedback_type == 'thumbs-up' and feedback_value == 'True':
            feedback_context.append(f"✅ User liked response to: '{query}...'")
        elif feedback_type == 'thumbs-down' or feedback_value == 'False':
            feedback_context.append(f"❌ User disliked response to: '{query}...'")
    
    if feedback_context:
        print("✅ Feedback context generated:")
        for context in feedback_context:
            print(f"   {context}")
    else:
        print("❌ No feedback context generated")

def cleanup_test_data(redis_client, user_id="test_user", chat_id="test_chat"):
    """Clean up test data"""
    if not redis_client:
        return
    
    session_key = f"session:{user_id}:{chat_id}"
    feedback_key = f"feedback:{user_id}:{chat_id}"
    
    redis_client.delete(session_key)
    redis_client.delete(feedback_key)
    print("\n🧹 Test data cleaned up")

if __name__ == "__main__":
    print("🚀 Testing Redis Fixes for Duplication and Feedback")
    print("=" * 60)
    
    # Test Redis connection
    redis_client = test_redis_connection()
    
    if redis_client:
        # Test conversation storage
        session_key = simulate_conversation_storage(redis_client)
        
        # Test feedback system
        test_feedback_system(redis_client, session_key=session_key)
        
        # Test feedback context generation
        test_feedback_context_generation(redis_client)
        
        # Cleanup
        cleanup_test_data(redis_client)
        
        print("\n✅ All tests completed!")
    else:
        print("\n❌ Cannot run tests without Redis connection")
