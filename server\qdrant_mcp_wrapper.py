#!/usr/bin/env python3
"""
Qdrant MCP Wrapper for main11.py
Provides a compatible interface that works with the existing main application
"""
import asyncio
import logging
from typing import List, Dict, Any
from server.qdrant_agent import query_qdrant_tool, test_qdrant_connection

logger = logging.getLogger(__name__)

class QdrantTool:
    """Mock tool class that mimics the expected interface"""
    
    def __init__(self):
        self.name = "qdrant_find"
        self.description = "Search Qdrant vector database for relevant content"
    
    async def coroutine(self, query: str, collection_name: str = None) -> str:
        """Execute the Qdrant search tool"""
        try:
            result = await query_qdrant_tool(query, collection_name)
            return result
        except Exception as e:
            logger.error(f"Error in Qdrant tool execution: {e}")
            return f"Error: {str(e)}"

class QdrantMCPClient:
    """Custom MCP client wrapper for Qdrant that provides the expected interface"""
    
    def __init__(self):
        self.qdrant_tool = QdrantTool()
        logger.info("Qdrant MCP client wrapper initialized")
    
    async def get_tools(self) -> List[QdrantTool]:
        """Return list of available tools with timeout"""
        try:
            # Test connection first with timeout
            connection_test = await asyncio.wait_for(test_qdrant_connection(), timeout=15.0)
            if connection_test:
                return [self.qdrant_tool]
            else:
                logger.error("Qdrant connection test failed")
                return []
        except asyncio.TimeoutError:
            logger.error("Get tools timed out after 15 seconds")
            return []
        except Exception as e:
            logger.error(f"Error getting tools: {e}")
            return []
    
    async def test_connection(self) -> bool:
        """Test the Qdrant MCP connection with timeout"""
        try:
            # Add timeout to prevent hanging
            result = await asyncio.wait_for(test_qdrant_connection(), timeout=20.0)
            return result
        except asyncio.TimeoutError:
            logger.error("Connection test timed out after 20 seconds")
            return False
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False

# Global instance for main11.py
qdrant_mcp_client = QdrantMCPClient()

async def get_qdrant_tool():
    """Get the Qdrant tool for main11.py"""
    try:
        # Return the tool directly since we know it exists
        return qdrant_mcp_client.qdrant_tool
    except Exception as e:
        logger.error(f"Error fetching tools: {str(e)}")
        raise

async def test_qdrant_mcp_connection():
    """Test Qdrant MCP connection for main11.py - lightweight version for startup"""
    try:
        logger.info("🔗 Testing Qdrant MCP connection...")

        # Quick check - just verify the components exist without full connection test
        from pathlib import Path
        import os

        # Check if server script exists
        server_script = Path(__file__).parent / "qdrant.py"
        if not server_script.exists():
            logger.error("❌ Qdrant server script not found")
            print("❌ Qdrant MCP Status: SERVER SCRIPT NOT FOUND")
            return False

        # Check environment variables
        qdrant_url = os.getenv("QDRANT_URL")
        qdrant_api_key = os.getenv("QDRANT_API_KEY")

        if not qdrant_url:
            logger.error("❌ QDRANT_URL not found in environment")
            print("❌ Qdrant MCP Status: MISSING QDRANT_URL")
            return False

        if not qdrant_api_key:
            logger.error("❌ QDRANT_API_KEY not found in environment")
            print("❌ Qdrant MCP Status: MISSING QDRANT_API_KEY")
            return False

        # Basic component check passed
        logger.info("✅ Qdrant MCP components available")
        print("✅ Qdrant MCP Status: COMPONENTS READY")
        return True

    except Exception as e:
        logger.error(f"❌ Qdrant MCP test failed: {str(e)}")
        print("❌ Qdrant MCP Status: CONNECTION FAILED")
        return False

async def test_qdrant_mcp_connection_full():
    """Full Qdrant MCP connection test - use this for detailed testing"""
    try:
        logger.info("🔗 Testing full Qdrant MCP connection...")

        # Add timeout to prevent hanging
        async def _test_with_timeout():
            # Test connection
            if not await qdrant_mcp_client.test_connection():
                logger.error("❌ Qdrant MCP connection test failed")
                return False

            # Get tools
            tools = await qdrant_mcp_client.get_tools()
            logger.info(f"✅ Available MCP tools: {[tool.name for tool in tools]}")

            # Check for qdrant_find tool
            qdrant_tool = next((tool for tool in tools if tool.name == "qdrant_find"), None)
            if not qdrant_tool:
                logger.error("❌ qdrant_find tool not found in MCP client")
                return False

            logger.info(f"✅ Qdrant MCP full test successful")
            return True

        # Run with 30 second timeout
        result = await asyncio.wait_for(_test_with_timeout(), timeout=30.0)
        return result

    except asyncio.TimeoutError:
        logger.error("❌ Qdrant MCP full test timed out after 30 seconds")
        return False
    except Exception as e:
        logger.error(f"❌ Qdrant MCP full test failed: {str(e)}")
        return False
