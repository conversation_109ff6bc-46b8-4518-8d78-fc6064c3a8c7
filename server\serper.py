#!/usr/bin/env python3
"""
Serper MCP Server with stdio transport
This file is started automatically by serper_agent.py
"""
import logging
import httpx
import os
import sys
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP

# Load environment variables
load_dotenv()

# Configure logging to stderr to avoid interfering with stdio
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s %(levelname)s %(message)s',
    stream=sys.stderr
)

# Initialize MCP server
mcp = FastMCP("serper")

# Environment variables
SERPER_API_KEY = os.environ.get("SERPER_API_KEY")

if not SERPER_API_KEY:
    print("ERROR: SERPER_API_KEY not found in environment!", file=sys.stderr)
    sys.exit(1)

@mcp.tool()
async def google_search(query: str, num_results: int = 5) -> list:
    """
    Search Google using Serper API.
    
    Args:
        query: The search query string
        num_results: Number of results to return (default: 5, max: 10)
    
    Returns:
        List of search results with title, link, snippet
    """
    # Limit num_results
    num_results = max(1, min(num_results, 10))
    
    headers = {
        "X-API-KEY": SERPER_API_KEY,
        "Content-Type": "application/json"
    }
    
    payload = {
        "q": query,
        "num": num_results
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://google.serper.dev/search", 
                headers=headers, 
                json=payload, 
                timeout=20.0
            )
            response.raise_for_status()
            data = response.json()
            
            # Extract organic search results
            organic_results = data.get("organic", [])
            if not organic_results:
                return []
            
            # Format results
            formatted_results = []
            for i, item in enumerate(organic_results[:num_results]):
                result = {
                    "title": item.get("title", "No title"),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "position": item.get("position", i + 1),
                    "metadata": {
                        "source": "google_search",
                        "search_engine": "serper"
                    }
                }
                formatted_results.append(result)
            
            return formatted_results
            
    except Exception as e:
        return [{"error": f"Search failed: {str(e)}"}]

if __name__ == "__main__":
    # Start server with stdio transport
    mcp.run(transport="stdio")
