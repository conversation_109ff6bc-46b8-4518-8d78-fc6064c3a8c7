#!/usr/bin/env python3
"""
Aggregator Agent - Multi-Agent RAG Architecture
Coordinates with sub-agents (<PERSON>dra<PERSON> and <PERSON><PERSON>) following the diagram flow
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from server.qdrant_agent import query_qdrant_tool
from server.serper_agent import query_google_search
from server.dynamic_agent_manager import query_user_dynamic_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AggregatorAgent")

class AggregatorAgent:
    """
    Main aggregator agent that coordinates with sub-agents
    Follows the Multi-Agent RAG architecture diagram
    """
    
    def __init__(self, memory_manager=None, planning_manager=None):
        self.memory_manager = memory_manager
        self.planning_manager = planning_manager
        self.sub_agents = {
            "qdrant": self._query_qdrant_agent,
            "serper": self._query_serper_agent
        }
        logger.info("Aggregator Agent initialized")
    
    async def _query_qdrant_agent(self, query: str, collection_name: str = None) -> Dict[str, Any]:
        """Query the Qdrant sub-agent with enhanced source tracking"""
        try:
            logger.info(f"Querying Qdrant agent with: {query} in collection: {collection_name}")
            result = await query_qdrant_tool(query, collection_name)

            # Parse the result to extract structured data with enhanced source tracking
            if isinstance(result, str):
                lines = result.split('\n')
                documents = []
                best_score = 0.0
                context_items = []

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Enhanced parsing for new format: "text [Score: X.XXX] Source: file | Collection: collection"
                    if '[Score:' in line and 'Source:' in line:
                        try:
                            # Extract score
                            score_start = line.find('[Score:') + 7
                            score_end = line.find(']', score_start)
                            score_str = line[score_start:score_end].strip()
                            score = float(score_str) if score_str != 'N/A' else 0.0
                            best_score = max(best_score, score)

                            # Extract source file and collection
                            source_part = line.split('Source:')[1].strip()
                            if '|' in source_part and 'Collection:' in source_part:
                                source_file = source_part.split('|')[0].strip()
                                collection_part = source_part.split('Collection:')[1].strip()
                            else:
                                source_file = source_part
                                collection_part = collection_name or "Gen AI"

                            # Extract content (everything before [Score:)
                            content = line.split('[Score:')[0].strip()
                            # Remove numbering if present (e.g., "1. content" -> "content")
                            if content and content[0].isdigit() and '. ' in content:
                                content = content.split('. ', 1)[1] if '. ' in content else content

                            # Add to documents with detailed info
                            documents.append({
                                "file": source_file,
                                "collection": collection_part,
                                "score": score,
                                "content_preview": content[:100] + "..." if len(content) > 100 else content
                            })

                            # Add to context items
                            context_items.append(content)

                            logger.info(f"Parsed Qdrant result: score={score:.3f}, file={source_file}, collection={collection_part}")

                        except Exception as parse_error:
                            logger.warning(f"Error parsing Qdrant result line '{line}': {parse_error}")
                            continue

                # For RAG mode: use any documents found, for other modes: use score threshold
                # This will be overridden by the calling function based on mode
                has_relevant_results = len(documents) > 0

                logger.info(f"Qdrant parsing complete: {len(documents)} documents, best_score={best_score:.3f}, has_relevant={has_relevant_results}")

                return {
                    "success": True,
                    "result": result,
                    "score": best_score,
                    "documents": documents,
                    "source": "qdrant",
                    "collection_used": collection_name or "Gen AI",
                    "has_relevant_results": has_relevant_results,
                    "context_items": context_items,
                    "total_documents_found": len(documents)
                }

            return {
                "success": True,
                "result": str(result),
                "score": 0.0,
                "documents": [],
                "source": "qdrant",
                "collection_used": collection_name or "Gen AI",
                "has_relevant_results": False,
                "context_items": [],
                "total_documents_found": 0
            }

        except Exception as e:
            logger.error(f"Error querying Qdrant agent: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "score": 0.0,
                "documents": [],
                "source": "qdrant",
                "collection_used": collection_name or "Gen AI",
                "has_relevant_results": False,
                "context_items": [],
                "total_documents_found": 0
            }
    
    async def _query_serper_agent(self, query: str, num_results: int = 3) -> Dict[str, Any]:
        """Query the Serper sub-agent with enhanced URL tracking"""
        try:
            logger.info(f"Querying Serper agent with: {query} (max {num_results} results)")
            result = await query_google_search(query, num_results)

            # Extract URLs and titles from the result with better parsing
            source_urls = []
            web_sources = []

            if isinstance(result, str):
                lines = result.split('\n')
                current_item = {}
                for line in lines:
                    line = line.strip()
                    if line.startswith('🔎 **') and line.endswith('**'):
                        # Extract title
                        title = line.replace('🔎 **', '').replace('**', '').strip()
                        current_item = {"title": title}
                    elif line.startswith('🔗 '):
                        # Extract URL
                        url = line.replace('🔗 ', '').strip()
                        if url.startswith('http') and len(source_urls) < 3:  # Limit to 3 URLs
                            source_urls.append(url)
                            if current_item:
                                current_item["url"] = url
                                web_sources.append(current_item)
                                current_item = {}

            # Ensure we have at most 3 URLs
            source_urls = source_urls[:3]
            web_sources = web_sources[:3]

            return {
                "success": True,
                "result": result,
                "source_urls": source_urls,
                "web_sources": web_sources,
                "source": "web",
                "total_results_found": len(source_urls)
            }

        except Exception as e:
            logger.error(f"Error querying Serper agent: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "source_urls": [],
                "web_sources": [],
                "source": "web",
                "total_results_found": 0
            }
    
    async def coordinate_query(self, query: str, mode: str = "agentic", 
                             collections: List[str] = None, user_id: str = None, **kwargs) -> Dict[str, Any]:
        """
        Enhanced Master Agent coordination with specialized dynamic agents
        Implements the Multi-Agent RAG architecture from diagram
        """
        logger.info(f"Master Agent coordinating: '{query}' (mode: {mode}, user: {user_id})")
        
        response = {
            "query": query,
            "mode": mode,
            "qdrant_result": None,
            "web_result": None,
            "dynamic_result": None,
            "final_context": [],
            "documents": [],
            "source_urls": [],
            "decision": "",
            "score": 0.0,
            "source": "",
            "decision_reason": "",
            "collections_searched": collections or ["Gen AI"],
            "web_sources": [],
            "agents_used": []
        }
        
        try:
            # Step 1: Check for user's specialized dynamic agent
            if user_id:
                logger.info(f"Checking for specialized agent for user {user_id}")
                dynamic_response = await query_user_dynamic_agent(user_id, query)
                response["dynamic_result"] = dynamic_response

                if dynamic_response["success"]:
                    response["final_context"].append(dynamic_response["result"])
                    response["source"] = f"specialized_agent"
                    response["score"] = 0.9  # High priority for user's own data
                    response["agents_used"].append(dynamic_response.get("agent_type", "dynamic"))

                    # If specialized agent provides good answer, prioritize it
                    if len(dynamic_response["result"]) > 50:  # Substantial response
                        response["decision"] = "specialized_agent_primary"
                        logger.info(f"Specialized agent provided primary response")
                        return response
                else:
                    logger.info(f"No specialized agent found or error for user {user_id}: {dynamic_response.get('error', 'Unknown error')}")
            
            # Step 2: Use existing Qdrant agent (Local Data Sources)
            if mode in ["rag", "agentic"]:
                collection_name = collections[0] if collections else "Gen AI"
                qdrant_response = await self._query_qdrant_agent(query, collection_name)
                response["qdrant_result"] = qdrant_response
                response["agents_used"].append("qdrant_agent")
                
                if qdrant_response["success"]:
                    response["score"] = max(response["score"], qdrant_response["score"])
                    response["documents"] = qdrant_response["documents"]
                    response["final_context"].append(qdrant_response["result"])
            
            # Step 3: Decision making based on Multi-Agent RAG architecture
            if response["dynamic_result"] and response["dynamic_result"]["success"]:
                if response["score"] >= 0.8:
                    response["decision"] = "specialized_with_context"
                    response["source"] = "specialized+qdrant"
                else:
                    response["decision"] = "specialized_primary"
                    response["source"] = "specialized"
            elif mode == "rag":
                response["decision"] = "rag_only"
                response["source"] = "qdrant"
            elif mode == "agentic":
                # Fixed agentic logic: use score threshold of 0.25
                if response["score"] >= 0.25:
                    response["decision"] = "local_sufficient"
                    response["source"] = "qdrant"
                    response["source_urls"] = []  # Clear any web URLs
                    # Keep only Qdrant results in final_context
                    logger.info(f"[Agentic] Score: {response['score']:.3f} -> Decision: local_sufficient (using Qdrant only)")
                else:
                    response["decision"] = "needs_web_search"
                    logger.info(f"[Agentic] Score: {response['score']:.3f} -> Decision: needs_web_search")
                    # Use Serper agent for web search
                    web_response = await self._query_serper_agent(query)
                    response["web_result"] = web_response
                    response["agents_used"].append("serper_agent")

                    if web_response["success"]:
                        response["final_context"] = [web_response["result"]]  # Use only web results
                        response["source_urls"] = web_response.get("source_urls", [])
                        response["source"] = "web"
                        response["documents"] = []  # Clear Qdrant documents when using web
                    else:
                        response["source"] = "qdrant"  # Fallback to Qdrant if web fails
                        response["source_urls"] = []
            elif mode == "web":
                response["decision"] = "web_only"
                response["source"] = "web"
                # Use Serper agent for web search
                web_response = await self._query_serper_agent(query)
                response["web_result"] = web_response
                response["agents_used"].append("serper_agent")

                if web_response["success"]:
                    response["final_context"] = [web_response["result"]]
                    response["source_urls"] = web_response.get("source_urls", [])
                    response["source"] = "web"
            
            # Step 4: Final source attribution (don't override mode-specific sources)
            # Only combine sources if it's truly a multi-agent scenario
            if mode == "agentic" and len(response["agents_used"]) > 1:
                # For agentic mode, this should not happen with the new logic
                # But if it does, keep the primary source
                pass  # Source already set correctly above
            
            logger.info(f"Master Agent decision: {response['decision']} using {response['agents_used']}")
            return response
            
        except Exception as e:
            logger.error(f"Error in Master Agent coordination: {e}")
            response["decision"] = "error"
            response["final_context"] = [f"Error processing query: {str(e)}"]
            return response
    
    
    def get_memory_context(self, chat_id: str, user_id: str) -> str:
        """Get memory context from memory manager"""
        try:
            if self.memory_manager:
                return self.memory_manager.get_context(chat_id, user_id)
            return ""
        except Exception as e:
            logger.error(f"Error getting memory context: {e}")
            return ""
    
    def store_interaction(self, query: str, response: str, chat_id: str, user_id: str, metadata: Dict = None):
        """Store interaction in memory manager"""
        if self.memory_manager:
            self.memory_manager.store_interaction(query, response, chat_id, user_id, metadata)

# Global aggregator agent instance
aggregator_agent = None

def get_aggregator_agent(memory_manager=None, planning_manager=None) -> AggregatorAgent:
    """Get or create aggregator agent instance"""
    global aggregator_agent
    if aggregator_agent is None:
        aggregator_agent = AggregatorAgent(memory_manager, planning_manager)
    return aggregator_agent

async def test_aggregator_agent():
    """Test the aggregator agent with real queries"""
    print("🤖 AGGREGATOR AGENT TEST")
    print("=" * 60)

    try:
        # Initialize agent
        agent = get_aggregator_agent()
        print("✅ Aggregator agent initialized")

        # Test different modes with real queries
        test_queries = [
            {
                "query": "Who is Sundar Pichai?",
                "mode": "rag",
                "description": "RAG Mode - Should search Qdrant only"
            },
            {
                "query": "Latest news about artificial intelligence 2024",
                "mode": "web",
                "description": "Web Mode - Should search web only"
            },
            {
                "query": "What is machine learning and how does it work?",
                "mode": "agentic",
                "description": "Agentic Mode - Should try Qdrant first, then web if needed"
            },
            {
                "query": "Explain neural networks",
                "mode": "agentic",
                "description": "Agentic Mode - Technical query test"
            }
        ]

        for i, test_case in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}: {test_case['description']}")
            print(f"Query: '{test_case['query']}'")
            print(f"Mode: {test_case['mode']}")
            print("-" * 40)

            try:
                # Test coordination
                result = await agent.coordinate_query(
                    query=test_case['query'],
                    mode=test_case['mode'],
                    collections=["Gen AI"]
                )

                # Display results
                print(f"✅ Coordination successful!")
                print(f"   Decision: {result['decision']}")
                print(f"   Score: {result['score']:.3f}")
                print(f"   Final context items: {len(result['final_context'])}")
                print(f"   Documents found: {len(result['documents'])}")
                print(f"   Source URLs: {len(result['source_urls'])}")

                # Show first 100 chars of context if available
                if result['final_context']:
                    context_preview = result['final_context'][0][:100] + "..." if len(result['final_context'][0]) > 100 else result['final_context'][0]
                    print(f"   Context preview: {context_preview}")

                # Show documents if available
                if result['documents']:
                    print(f"   Document sources: {[doc.get('file', 'unknown') for doc in result['documents'][:3]]}")

                # Show URLs if available
                if result['source_urls']:
                    print(f"   Web sources: {len(result['source_urls'])} URLs found")

            except Exception as e:
                print(f"❌ Test failed: {str(e)}")

            print()

        # Test memory context (if available)
        print("🧠 Testing Memory Context...")
        try:
            test_chat_id = "507f1f77bcf86cd799439011"  # Sample ObjectId
            test_user_id = "user123"
            context = agent.get_memory_context(test_chat_id, test_user_id)
            print(f"✅ Memory context retrieved: {len(context)} characters")
        except Exception as e:
            print(f"⚠️ Memory context test: {str(e)}")

        print("\n" + "=" * 60)
        print("🎉 Aggregator Agent testing completed!")
        print("📝 Note: Some tests may show errors if MCP servers are not running")
        print("📝 To run MCP servers:")
        print("   - Terminal 1: python server/qdrant.py")
        print("   - Terminal 2: python server/serper.py")

    except Exception as e:
        print(f"❌ Aggregator agent test failed: {str(e)}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install fastapi uvicorn python-dotenv pymongo redis")

if __name__ == "__main__":
    print("🚀 Starting Aggregator Agent Test...")
    asyncio.run(test_aggregator_agent())
