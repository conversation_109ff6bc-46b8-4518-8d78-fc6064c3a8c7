#!/usr/bin/env python3
"""
Generative Model Component - Streaming LLM Response Generation
Handles final response generation with streaming support for Multi-Agent RAG
"""
import asyncio
import json
import logging
import requests
import time
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime
import os
from dotenv import load_dotenv
#from app.format_answer import enforce_structured_format

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GenerativeModel")

class GenerativeModel:
    """
    Generative model component that produces final responses with streaming support
    Integrates with multiple LLM providers
    """
    
    def __init__(self):
        # LLM endpoints
        self.endpoints = {
            "spaarxsenseaifabric": os.getenv("SPAARXSENSE_AI_FABRIC_ENDPOINT"),
            "groq": os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1/chat/completions"),
            "google": os.getenv("GOOGLE_ENDPOINT", "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent"),
            "openai": os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1/chat/completions"),
            "anthropic": os.getenv("ANTHROPIC_ENDPOINT", "https://api.anthropic.com/v1/messages"),
            "huggingface": os.getenv("HUGGINGFACE_ENDPOINT", "https://api-inference.huggingface.co/models")
        }
        
        self.max_tokens = 4096
        logger.info("Generative Model initialized")
    
    def create_prompt(self, query: str, context: List[str], history: str = "",
                     mode: str = "agentic", host: str = "groq") -> str:
        """
        Create optimized prompt for the LLM with size limiting

        Args:
            query: User query
            context: List of context strings from sub-agents
            history: Chat history
            mode: Query mode (rag, agentic, web)
            host: LLM host (for size optimization)

        Returns:
            Formatted prompt string
        """
        # Apply context size limits based on host
        if host == "groq":
            max_context_items = 3
            max_history_length = 800
        else:
            max_context_items = 5
            max_history_length = 1500

        # Limit context items
        if context and len(context) > max_context_items:
            context = context[:max_context_items]
            logger.info(f"Limited context to {max_context_items} items for {host}")

        # Limit history length
        if history and len(history) > max_history_length:
            history = history[-max_history_length:]
            logger.info(f"Truncated history to {max_history_length} chars for {host}")

        context_str = "\n".join([f"- {c}" for c in context]) if context else "No relevant information found."

        if mode == "rag" and not context:
            return "Sorry, no relevant information was found in the provided documents."
        
        prompt = f"""You are a helpful AI assistant providing accurate and concise responses.

Response Instructions:

You are an intelligent assistant that answers user queries using only the information retrieved from context, chat history, or MCP tools. Do not use general knowledge.

- When mode is 'rag', use ONLY the provided Context from Qdrant documents to answer the query.
- In rag mode, if no Context is provided or Context is empty, respond EXACTLY with:
"Sorry, no relevant information was found in the provided documents."
- In rag mode, DO NOT use any general knowledge, outside information, or assumptions.
- When mode is 'agentic', use Context first; if insufficient, use web search results.
- When mode is 'web', use ONLY the provided web search results to answer the query.
- Base your response on the provided Context and History.


Answering Rules:
- Respond concisely and factually based on the Context/History search.
- Give the answer clean and accurate.
- Never use outside knowledge.
- Do not include any URLs inside the main answer.
- Ensure the answer is factual, clear and complete.
- Do not print this in my answer 'Based on the context and chat history'.
- Do not include any Response Prompt Instructions in the final answer — only return the exact answer.
- Do not give any questions/querys in the final answer.

**Context:**
{context_str}

**Chat History:**
{history}

**User Query:**
{query}

**Response:**"""
        
        return prompt
    
    async def generate_response(self, query: str, context: List[str], host: str, 
                              model: str, api_key: str, history: str = "", 
                              mode: str = "agentic", stream: bool = False) -> str:
        """
        Generate response using specified LLM
        
        Args:
            query: User query
            context: Context from sub-agents
            host: LLM provider
            model: Model name
            api_key: API key
            history: Chat history
            mode: Query mode
            stream: Whether to stream response
            
        Returns:
            Generated response
        """
        prompt = self.create_prompt(query, context, history, mode, host)
        
        if mode == "rag" and not context:
            return "Sorry, no relevant information was found in the provided documents."
        
        try:
            if stream:
                # For streaming, we'll collect the full response for now
                response = await self._call_llm(host, model, api_key, prompt, stream=False)
                return response
                #return enforce_structured_format(response)
            else:
                return await self._call_llm(host, model, api_key, prompt, stream=False)
                #response = await self._call_llm(host, model, api_key, prompt, stream=False)
                #return (response)
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error generating response: {str(e)}"
    
    async def generate_streaming_response(self, query: str, context: List[str], 
                                        host: str, model: str, api_key: str, 
                                        history: str = "", mode: str = "agentic") -> AsyncGenerator[str, None]:
        """
        Generate streaming response
        
        Args:
            query: User query
            context: Context from sub-agents
            host: LLM provider
            model: Model name
            api_key: API key
            history: Chat history
            mode: Query mode
            
        Yields:
            Response chunks
        """
        prompt = self.create_prompt(query, context, history, mode)
        
        if mode == "rag" and not context:
            yield "Sorry, no relevant information was found in the provided documents."
            return
        
        try:
            # For demonstration, we'll simulate streaming by chunking the response
            full_response = await self._call_llm(host, model, api_key, prompt, stream=False)
            
            # Apply post-processing for structure
            #formatted_response = enforce_structured_format(full_response)

            # Simulate streaming by yielding words
            words = full_response.split()
            #words = formatted_response.split()
            for i, word in enumerate(words):
                if i == 0:
                    yield word
                else:
                    yield f" {word}"
                await asyncio.sleep(0.05)  # Small delay to simulate streaming
                
        except Exception as e:
            logger.error(f"Error in streaming response: {e}")
            yield f"Error generating response: {str(e)}"
    
    async def _call_llm(self, host: str, model: str, api_key: str, prompt: str, 
                       stream: bool = False) -> str:
        """
        Call the specified LLM provider
        
        Args:
            host: LLM provider
            model: Model name
            api_key: API key
            prompt: Input prompt
            stream: Whether to stream
            
        Returns:
            LLM response
        """
        headers = {"Content-Type": "application/json"}
        
        if host == "spaarxsenseaifabric":
            return await self._call_spaarxsense(model, api_key, prompt, headers)
        elif host == "groq":
            return await self._call_groq(model, api_key, prompt, headers)
        elif host == "google":
            return await self._call_google(model, api_key, prompt, headers)
        elif host == "openai":
            return await self._call_openai(model, api_key, prompt, headers)
        elif host == "anthropic":
            return await self._call_anthropic(model, api_key, prompt, headers)
        elif host in ["meta", "microsoft"]:
            return await self._call_huggingface(host, model, api_key, prompt, headers)
        else:
            raise ValueError(f"Unsupported host: {host}")
    
    async def _call_spaarxsense(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call SpaarxSense AI Fabric"""
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post(self.endpoints["spaarxsenseaifabric"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("response", "No response generated.")
    
    async def _call_groq(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call Groq API with context size limiting"""
        # Groq has strict payload size limits, so we need to truncate long prompts
        max_prompt_length = 6000  # Conservative limit for Groq

        if len(prompt) > max_prompt_length:
            logger.warning(f"Groq: Prompt too long ({len(prompt)} chars), truncating to {max_prompt_length}")
            # Try to preserve the query and instructions while truncating context
            lines = prompt.split('\n')
            query_line = ""
            instructions = []
            context_lines = []

            for line in lines:
                if line.startswith("Query:"):
                    query_line = line
                elif line.startswith("**Instructions") or line.startswith("You are"):
                    instructions.append(line)
                elif line.startswith("Context:") or line.startswith("History:"):
                    context_lines.append(line)
                    break
                else:
                    instructions.append(line)

            # Rebuild prompt with truncated context
            essential_parts = '\n'.join(instructions) + '\n' + query_line
            remaining_length = max_prompt_length - len(essential_parts) - 100  # Buffer

            if remaining_length > 0 and context_lines:
                truncated_context = prompt[prompt.find("Context:"):][:remaining_length]
                prompt = essential_parts + '\n' + truncated_context
            else:
                prompt = essential_parts

            logger.info(f"Groq: Truncated prompt to {len(prompt)} characters")

        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "stream": False,
            "max_tokens": min(self.max_tokens, 2048)  # Groq prefers smaller max_tokens
        }
        headers["Authorization"] = f"Bearer {api_key}"

        # Retry logic for rate limiting
        max_retries = 2
        retry_delay = 1  # seconds

        for attempt in range(max_retries + 1):
            try:
                response = requests.post(self.endpoints["groq"],
                                       json=payload, headers=headers, timeout=30)
                response.raise_for_status()
                data = response.json()
                return data.get("choices", [{}])[0].get("message", {}).get("content", "No response generated.")
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 413:
                    logger.error("Groq: Payload still too large after truncation, using fallback response")
                    return "I apologize, but the context is too large for processing. Please try with a shorter query or fewer documents."
                elif e.response.status_code == 429:
                    if attempt < max_retries:
                        logger.warning(f"Groq: Rate limit exceeded, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries + 1})")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                        continue
                    else:
                        logger.error("Groq: Rate limit exceeded after all retries, using fallback response")
                        return "I apologize, but the service is currently experiencing high demand. Please try again in a few moments, or consider using a different model."
                else:
                    logger.error(f"Groq API error {e.response.status_code}: {e}")
                    return f"I apologize, but there was an error processing your request. Please try again later."
            except Exception as e:
                logger.error(f"Groq: Unexpected error: {e}")
                return f"I apologize, but there was an unexpected error. Please try again later."
    
    async def _call_google(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call Google Gemini API"""
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {"maxOutputTokens": self.max_tokens}
        }
        headers["x-goog-api-key"] = api_key
        
        response = requests.post(self.endpoints["google"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "No response generated.")
    
    async def _call_openai(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call OpenAI API"""
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "stream": False,
            "max_tokens": self.max_tokens
        }
        headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post(self.endpoints["openai"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("choices", [{}])[0].get("message", {}).get("content", "No response generated.")
    
    async def _call_anthropic(self, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call Anthropic Claude API"""
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": self.max_tokens
        }
        headers.update({
            "x-api-key": api_key,
            "anthropic-version": "2023-06-01"
        })
        
        response = requests.post(self.endpoints["anthropic"], 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("content", [{}])[0].get("text", "No response generated.")
    
    async def _call_huggingface(self, host: str, model: str, api_key: str, prompt: str, headers: Dict) -> str:
        """Call HuggingFace API for Meta/Microsoft models"""
        payload = {"inputs": prompt, "parameters": {"max_new_tokens": self.max_tokens}}
        headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post(f"{self.endpoints['huggingface']}/{model}", 
                               json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            return data[0].get("generated_text", "No response generated.")
        return "No response generated."
    
    def validate_response(self, response: str, mode: str, context: List[str]) -> str:
        """
        Validate and clean the generated response

        Args:
            response: Generated response
            mode: Query mode
            context: Original context

        Returns:
            Validated response
        """
        if not response or not response.strip():
            if mode == "rag":
                return "Sorry, no relevant information was found in the provided documents."
            else:
                return "I apologize, but I couldn't generate a proper response. Please try again."

        # Clean up common issues
        response = response.strip()

        # Enhanced RAG mode validation - prevent general knowledge responses
        if mode == "rag":
            # If no context was provided, return fallback message
            if not context or not any(ctx.strip() for ctx in context):
                return "Sorry, no relevant information was found in the provided documents."

            # Check if response seems to be using general knowledge
            general_knowledge_indicators = [
                "based on my knowledge",
                "generally speaking",
                "in general",
                "typically",
                "usually",
                "commonly",
                "it is known that",
                "as far as I know"
            ]

            response_lower = response.lower()
            if any(indicator in response_lower for indicator in general_knowledge_indicators):
                logger.warning("RAG mode: Detected potential general knowledge response, returning fallback")
                return "Sorry, no relevant information was found in the provided documents."

        # Web mode has no fallback - just return the response as-is

        # Remove any system prompts that might have leaked through
        if response.startswith("You are a helpful"):
            lines = response.split('\n')
            for i, line in enumerate(lines):
                if line.strip() and not line.startswith("You are") and not line.startswith("**"):
                    response = '\n'.join(lines[i:])
                    break

        return response

# Global generative model instance
generative_model = None

def get_generative_model() -> GenerativeModel:
    """Get or create generative model instance"""
    global generative_model
    if generative_model is None:
        generative_model = GenerativeModel()
    return generative_model

async def test_generative_model():
    """Test the generative model with comprehensive scenarios"""
    print("🤖 GENERATIVE MODEL TEST")
    print("=" * 60)

    try:
        # Initialize generative model
        model = get_generative_model()
        print("✅ Generative model initialized")

        # Test contexts and queries
        test_scenarios = [
            {
                "query": "What is machine learning?",
                "context": ["Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed."],
                "mode": "rag",
                "description": "RAG mode with good context"
            },
            {
                "query": "What is quantum computing?",
                "context": [],
                "mode": "rag",
                "description": "RAG mode with no context (should return fallback)"
            },
            {
                "query": "Latest developments in AI",
                "context": ["Recent AI developments include large language models, computer vision advances, and robotics improvements."],
                "mode": "web",
                "description": "Web mode with context"
            },
            {
                "query": "How does neural network work?",
                "context": ["Neural networks are computing systems inspired by biological neural networks.", "They consist of layers of interconnected nodes that process information."],
                "mode": "agentic",
                "description": "Agentic mode with multiple context items"
            }
        ]

        print(f"\n🧪 Testing {len(test_scenarios)} different scenarios...")

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🔍 Test {i}: {scenario['description']}")
            print(f"Query: '{scenario['query']}'")
            print(f"Mode: {scenario['mode']}")
            print(f"Context items: {len(scenario['context'])}")
            print("-" * 40)

            try:
                # Test 1: Prompt creation
                prompt = model.create_prompt(
                    query=scenario['query'],
                    context=scenario['context'],
                    history="User: Hello\nAssistant: Hi! How can I help you?",
                    mode=scenario['mode']
                )
                print(f"✅ Prompt created: {len(prompt)} characters")

                # Show prompt preview
                prompt_preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
                print(f"   Preview: {prompt_preview}")

                # Test 2: Response validation
                test_responses = [
                    "This is a good response about machine learning.",
                    "",  # Empty response
                    "You are a helpful assistant. Machine learning is...",  # Response with system prompt
                    "Sorry, no relevant information was found."  # Fallback response
                ]

                print(f"✅ Response validation tests:")
                for j, test_response in enumerate(test_responses):
                    validated = model.validate_response(test_response, scenario['mode'], scenario['context'])
                    status = "✅" if validated != test_response else "➡️"
                    print(f"   {status} Test {j+1}: {len(validated)} chars")

                # Test 3: Streaming simulation (without actual API call)
                print(f"✅ Streaming simulation:")
                print("   Response: ", end="")

                # Simulate streaming with a mock response
                mock_response = f"This is a test response for the query about {scenario['query'].split()[-1] if scenario['query'].split() else 'the topic'}. The response demonstrates the streaming functionality."

                words = mock_response.split()
                for k, word in enumerate(words[:10]):  # Limit to first 10 words for testing
                    if k == 0:
                        print(word, end="", flush=True)
                    else:
                        print(f" {word}", end="", flush=True)
                    await asyncio.sleep(0.01)  # Small delay to simulate streaming

                if len(words) > 10:
                    print("...", end="")
                print()  # New line

            except Exception as e:
                print(f"❌ Test failed: {str(e)}")

        # Test 4: Different LLM provider configurations
        print(f"\n🔧 Test: LLM Provider Configurations")
        print("-" * 30)

        providers = [
            {"host": "groq", "model": "llama3-8b-8192"},
            {"host": "openai", "model": "gpt-3.5-turbo"},
            {"host": "google", "model": "gemini-pro"},
            {"host": "anthropic", "model": "claude-3-sonnet"},
            {"host": "spaarxsenseaifabric", "model": "deepseek-r1:1.5b"}
        ]

        for provider in providers:
            try:
                # Test prompt creation for each provider
                test_prompt = model.create_prompt(
                    "Test query",
                    ["Test context"],
                    "",
                    "agentic"
                )
                print(f"✅ {provider['host']}: Prompt ready for {provider['model']}")
            except Exception as e:
                print(f"⚠️ {provider['host']}: Configuration issue - {str(e)}")

        # Test 5: Different modes
        print(f"\n🎯 Test: Mode-Specific Behavior")
        print("-" * 30)

        test_query = "What is artificial intelligence?"
        modes = ["rag", "agentic", "web"]

        for mode in modes:
            # Test with context
            prompt_with_context = model.create_prompt(test_query, ["AI is a technology"], "", mode)
            # Test without context
            prompt_without_context = model.create_prompt(test_query, [], "", mode)

            print(f"✅ {mode.upper()} mode:")
            print(f"   With context: {len(prompt_with_context)} chars")
            print(f"   Without context: {len(prompt_without_context)} chars")

            # Special handling for RAG mode without context
            if mode == "rag":
                no_context_response = model.create_prompt(test_query, [], "", mode)
                if "Sorry, no relevant information" in no_context_response:
                    print(f"   ✅ RAG fallback message working correctly")

        print("\n" + "=" * 60)
        print("🎉 Generative Model testing completed!")
        print("📊 Summary:")
        print(f"   - Tested prompt creation for different modes and contexts")
        print(f"   - Validated response cleaning and formatting")
        print(f"   - Simulated streaming functionality")
        print(f"   - Verified provider configurations")
        print("📝 Note: Actual LLM API calls require valid API keys and network access")

    except Exception as e:
        print(f"❌ Generative model test failed: {str(e)}")
        print("💡 Make sure dependencies are installed:")
        print("   pip install requests python-dotenv")

if __name__ == "__main__":
    print("🚀 Starting Generative Model Test...")
    asyncio.run(test_generative_model())
