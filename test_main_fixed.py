#!/usr/bin/env python3
"""
Test script to verify the fixed main11.py MCP configuration works
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_fixed_mcp_config():
    """Test the fixed MCP configuration"""
    try:
        print("🔍 Testing Fixed Qdrant MCP Configuration")
        print("=" * 60)
        
        # Import the wrapper functions
        from server.qdrant_mcp_wrapper import qdrant_mcp_client, test_qdrant_mcp_connection, get_qdrant_tool
        
        print("✅ Imports successful")
        
        # Test connection
        print("\n1. Testing connection...")
        connection_result = await test_qdrant_mcp_connection()
        if connection_result:
            print("✅ Connection test PASSED")
        else:
            print("❌ Connection test FAILED")
            return False
        
        # Test getting tools
        print("\n2. Testing tool retrieval...")
        qdrant_tool = await get_qdrant_tool()
        print(f"✅ Tool retrieved: {qdrant_tool.name}")
        
        # Test tool execution
        print("\n3. Testing tool execution...")
        result = await qdrant_tool.coroutine(
            query="artificial intelligence",
            collection_name=os.getenv("COLLECTION_NAME", "Gen AI")
        )
        
        if "Error:" not in result:
            print("✅ Tool execution PASSED")
            print(f"📊 Result preview: {result[:200]}...")
        else:
            print(f"❌ Tool execution FAILED: {result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_fixed_mcp_config()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ ALL TESTS PASSED - main11.py should work correctly!")
        print("🚀 You can now start your FastAPI application")
    else:
        print("❌ TESTS FAILED - needs more fixing")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
