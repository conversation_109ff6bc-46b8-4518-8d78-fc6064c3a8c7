#!/usr/bin/env python3
"""
Test script to verify guardrails fixes for technical queries
"""
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_technical_detection():
    """Test the technical query detection"""
    from app.guardrail import is_technical_query
    
    test_cases = [
        ("What is Group Relative Policy Optimization (GRPO) in Deepseek-R1-Zero training?", True),
        ("How does the Mixture-of-Experts architecture enhance Deepseek-R1 performance?", True),
        ("Tell me about transformer architecture", True),
        ("What is my phone number?", False),
        ("Hello, how are you?", False),
        ("Explain neural networks and deep learning", True),
    ]
    
    print("🧪 Testing Technical Query Detection:")
    print("=" * 50)
    
    for query, expected in test_cases:
        result = is_technical_query(query)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} | Technical: {result} | Query: {query[:60]}...")
    
    print()

def test_pii_filter():
    """Test the PII filter with technical context"""
    try:
        from app.guardrail import pii_filter
        
        test_cases = [
            ("What is Group Relative Policy Optimization (GRPO) in Deepseek-R1-Zero training?", True, ""),
            ("How does the Mixture-of-Experts architecture enhance Deepseek-R1 performance?", True, ""),
            ("My <NAME_EMAIL>", False, "Personal or sensitive information detected."),
            ("Call me at ************", False, "Personal or sensitive information detected."),
        ]
        
        print("🔒 Testing PII Filter:")
        print("=" * 50)
        
        for query, is_technical, expected in test_cases:
            result = pii_filter(query, is_technical=is_technical)
            status = "✅ PASS" if result == expected else "❌ FAIL"
            print(f"{status} | Technical: {is_technical} | Result: '{result}' | Query: {query[:50]}...")
        
        print()
    except ImportError as e:
        print(f"⚠️ Could not test PII filter due to import error: {e}")
        print()

def test_full_guardrails():
    """Test the complete guardrails function"""
    try:
        from app.guardrail import run_guardrails
        
        test_cases = [
            ("What is Group Relative Policy Optimization (GRPO) in Deepseek-R1-Zero training?", True),
            ("How does the Mixture-of-Experts architecture enhance Deepseek-R1 performance?", True),
            ("Tell me about transformer architecture", True),
            ("My <NAME_EMAIL>", False),
        ]
        
        print("🛡️ Testing Full Guardrails:")
        print("=" * 50)
        
        for query, expected_safe in test_cases:
            try:
                is_safe, result = run_guardrails(query)
                status = "✅ PASS" if is_safe == expected_safe else "❌ FAIL"
                print(f"{status} | Safe: {is_safe} | Query: {query[:50]}...")
                if not is_safe:
                    print(f"    Blocked reason: {result[:100]}...")
            except Exception as e:
                print(f"❌ ERROR | Query: {query[:50]}... | Error: {e}")
        
        print()
    except ImportError as e:
        print(f"⚠️ Could not test full guardrails due to import error: {e}")
        print()

if __name__ == "__main__":
    print("🚀 Testing Guardrails Fixes for Technical Queries")
    print("=" * 60)
    print()
    
    test_technical_detection()
    test_pii_filter()
    test_full_guardrails()
    
    print("✅ Testing complete!")
