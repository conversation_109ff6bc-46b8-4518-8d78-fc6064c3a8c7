#!/usr/bin/env python3
"""
Test script for Qdrant MCP Server with stdio transport
Demonstrates the working functionality
"""
import asyncio
from server.qdrant_agent import query_qdrant_tool, test_qdrant_connection

async def main():
    print("🔍 Testing Qdrant MCP Server with stdio transport")
    print("=" * 60)
    
    # Test connection
    print("1. Testing connection...")
    if await test_qdrant_connection():
        print("✅ Connection successful!")
    else:
        print("❌ Connection failed!")
        return
    
    print("\n2. Testing queries...")
    
    # Test queries
    test_queries = [
        "artificial intelligence",
        "machine learning",
        "deep learning",
        "neural networks"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Query {i}: '{query}'")
        print("-" * 40)
        result = await query_qdrant_tool(query)
        print(result[:500] + "..." if len(result) > 500 else result)
        print()

if __name__ == "__main__":
    asyncio.run(main())
