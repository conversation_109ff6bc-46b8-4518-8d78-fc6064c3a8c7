#!/usr/bin/env python3
"""
Serper MCP Agent with stdio transport
No server URL needed - connects directly to server process
Run this: python serper_agent.py
"""
import asyncio

async def query_google_search(query: str, num_results: int = 3) -> str:
    """Query the Google Search MCP tool using stdio transport."""
    print(f"🔍 Searching for: {query}")

    try:
        # Try direct API call if MCP fails
        import httpx
        import os
        from dotenv import load_dotenv

        load_dotenv()
        SERPER_API_KEY = os.environ.get("SERPER_API_KEY")

        if not SERPER_API_KEY:
            return "❌ Serper API key not found"

        # Direct API call to Serper
        headers = {
            "X-API-KEY": SERPER_API_KEY,
            "Content-Type": "application/json"
        }

        payload = {
            "q": query,
            "num": min(num_results, 3)  # Limit to 3 results
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://google.serper.dev/search",
                headers=headers,
                json=payload,
                timeout=10.0
            )

            if response.status_code == 200:
                data = response.json()

                # Format results
                formatted_results = []

                # Add organic results
                if "organic" in data:
                    for i, result in enumerate(data["organic"][:3]):  # Limit to 3
                        title = result.get("title", "No title")
                        link = result.get("link", "")
                        snippet = result.get("snippet", "No description")

                        formatted_results.append(f"🔎 **{title}**")
                        formatted_results.append(f"{snippet}")
                        formatted_results.append(f"🔗 {link}")
                        formatted_results.append("")  # Empty line for separation

                if formatted_results:
                    return "\n".join(formatted_results)
                else:
                    return "No search results found."
            else:
                return f"❌ Search API error: {response.status_code}"

    except Exception as e:
        print(f"❌ Error in Google search: {e}")
        return f"❌ Search error: {str(e)}"

async def main():
    print("🔍 Serper MCP Agent - Google Search (stdio transport)")
    print("=" * 60)
    print("📝 Note: No server URL needed - connects directly to serper.py")
    print()
    

if __name__ == "__main__":
    asyncio.run(main())
